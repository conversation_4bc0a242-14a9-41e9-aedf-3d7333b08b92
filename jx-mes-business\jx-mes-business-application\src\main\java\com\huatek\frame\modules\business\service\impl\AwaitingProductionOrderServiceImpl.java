package com.huatek.frame.modules.business.service.impl;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.modules.bpm.constant.ApprovalStatus;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.bpm.dto.ProcessFormDTO;
import com.huatek.frame.modules.bpm.service.ProcessInstanceProxyService;
import com.huatek.frame.modules.bpm.service.ProcessPrivilegeService;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.business.utils.IDCardExcelExporter;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.SysProcessRecord;
import com.huatek.frame.modules.system.domain.vo.*;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.system.service.CommonFileService;
import com.huatek.frame.modules.system.service.DicDetailService;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.SysProcessRecordService;
import com.huatek.frame.modules.system.service.SysUserService;
import com.huatek.tool.modules.poi.DocumentUtils;
import com.huatek.tool.modules.poi.ReportDocxTool;
import com.huatek.tool.modules.poi.TempData;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.util.StringUtil;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;


/**
 * 待制工单 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "awaitingProductionOrder")
//@RefreshScope
@Slf4j
public class AwaitingProductionOrderServiceImpl implements AwaitingProductionOrderService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

	private String processBusinessKey = "待制工单";

	@DubboReference
	private ProcessInstanceProxyService processInstanceProxyService;
	@DubboReference
	private ProcessPrivilegeService processPrivilegeService;
	@Autowired
	private CustomerInformationManagementMapper customerInformationManagementMapper;
	@Autowired
	private ProductionOrderOperationHistoryMapper productionOrderOperationHistoryMapper;
	@Autowired
    private ProductListMapper productListMapper;
	@Autowired
	private ProductManagementMapper productManagementMapper;
	@DubboReference
    private SysGroupService sysGroupService;
	@DubboReference
	private DicDetailService dicDetailService;
	@Autowired
	private CapabilityReviewService capabilityReviewService;
	@DubboReference
	private CommonFileService commonFileService;
	@Autowired
	private EvaluationOrderMapper evaluationOrderMapper;
	@Autowired
	private CustomerProcessSchemeMapper customerProcessSchemeMapper;
	@Autowired
	private CustomerExperimentProjectMapper customerExperimentProjectMapper;
	@Autowired
	private CustomerExperimentProjectDataMapper customerExperimentProjectDataMapper;
	@Autowired
	private StandardProcessManagementMapper standardProcessManagementMapper;
	@Autowired
	private CodeManagementService codeManagementService;
	@Autowired
	private ExperimentProjectMapper experimentProjectMapper;
	@Autowired
	private WorkstationMapper workstationMapper;
	@Autowired
	private ProductInformationManagementMapper productInformationManagementMapper;

	@Autowired
	private OutsourcingService outsourcingService;
	@DubboReference
	private SysProcessRecordService sysProcessRecordService;
	@Autowired
	private ProductionTaskService productionTaskService;
	@Autowired
	private ProductionOrderResultService productionOrderResultService;
	@Autowired
	private ProductInventoryService productInventoryService;
	@Autowired
	private ProductionOrderProcessTestMapper productionOrderProcessTestMapper;
	@Autowired
	private FileReviewService fileReviewService;
	@Autowired
	private EquipmentInventoryMapper equipmentInventoryMapper;
	@Autowired
	private DeviceTraceabilityMapper deviceTraceabilityMapper;
	@Autowired
	private ReportManagementMapper reportManagementMapper ;
	@Autowired
	private StandardSpecificationMapper standardSpecificationMapper;
	@DubboReference
	private SysUserService sysUserService;
	@Autowired
	private AttachmentService attachmentService;
	@Autowired
	private ProductionOrderResultMapper productionOrderResultMapper ;
	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public AwaitingProductionOrderServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductionOrderVO>> findAwaitingProductionOrderPage(ProductionOrderDTO dto) {
		String currentUser = SecurityContextHolder.getCurrentUserName();
		//Codex - 流程人员流程查看数据权限控制
		if(!processPrivilegeService.hasViewProcessPrivilege(dto.getId(),currentUser,processBusinessKey)){
			log.info("用户无权限查看流程, currentUser:{},processBusinessKey:{}",currentUser,processBusinessKey);
			TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
			response.getData().setData(new ArrayList<>());
			response.setStatus(Constant.REQUEST_SUCCESS);
			response.getData().setCount(0L);
			return response;
		}

		//判断当前登录人是否是可靠性部门，如果是可靠性部门且角色不是调度则只查看负责人是当前登录人的数据,其他情况查询全部数据
		TorchResponse<SysGroupVO> group = sysGroupService.findGroup(SecurityContextHolder.getCurrentUserGroupId());
		//查询当前用户角色
		List<String> roles = awaitingProductionOrderMapper.selectCurrentUserRoles(SecurityContextHolder.getCurrentUserId());
		if(group.getData().getData().getGroupCode().equals(DicConstant.Group.GROUP_KEKAOXING) && !roles.contains(DicConstant.Role.ROLE_DIAODU)){
			dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserGroupId());
		}
		//Codex - 流程人员流程查看数据权限控制
		 if (dto.getWorkflowQueryRole().equals("applicant")){
			if(currentUser.equals(ProcessConstant.SUPER_USER)){
				dto.setCodexTorchApplicant("");
			}else{
				dto.setCodexTorchApplicant(currentUser);
			}
		}else if(dto.getWorkflowQueryRole().equals("approver")){
			//多审批人处理
			dto.setCodexTorchApprover(null);
			dto.setCodexTorchApprovers(currentUser);
			if(StringUtils.isEmpty(dto.getCodexTorchApprovalStatus()) || currentUser.equals(ProcessConstant.SUPER_USER)) {
				dto.setCodexTorchApprovalStatus("待审批|已审批|已驳回");
			}else {
				dto.setCodexTorchApprovalStatus(dto.getCodexTorchApprovalStatus());
			}
		}
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductionOrderVO> awaitingProductionOrders = awaitingProductionOrderMapper.selectAwaitingProductionOrderPage(dto);
		awaitingProductionOrders.stream().forEach(item -> {
			//处理标准规范
			if(!ObjectUtils.isEmpty(item.getStandardSpecificationId())){
				String[]  ids = item.getStandardSpecificationId().split(",");
				List<StandardSpecification> list = new ArrayList<>();
				for(String id : ids){
					StandardSpecification standardSpecification = standardSpecificationMapper.selectById(id);
					list.add(standardSpecification);
				}
				item.setStandardSpecifications(list);
			}
		});
		TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
		response.getData().setData(awaitingProductionOrders);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount(awaitingProductionOrders.getTotal());
		return response;
	}

	/**
	 * 多级审批，不同人看到的审批状态不同
	 *
	 * @param updateStatusVOVOList
	 * @param currentUser
	 */
	private void updateApproveStatusForCurrentUser(List<ProductionOrderVO> updateStatusVOVOList,String currentUser){
		for(ProductionOrderVO updateStatusVO : updateStatusVOVOList){
			String currentApprover = updateStatusVO.getCodexTorchApprover();
			String approvers = updateStatusVO.getCodexTorchApprovers();

			if(StringUtils.isEmpty(approvers)){
				continue;
			}

			//当前用户不在审批人之列，不能处理记录
			if(!approvers.contains(currentUser)){
				continue;
			}

			//当前用户是当前审批人,审批状态是待审批，不需要更新状态
			if(currentApprover.contains(currentUser)
					&& updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
				continue;
			}

			//如果当前用户处在当前审批者之前，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && (approvers.indexOf(currentApprover) > approvers.indexOf(currentUser))){
				if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
					updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
				}
			}

			//如果当前用户在已审批用户中但不是最后一个审批用户，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && !approvers.endsWith(currentUser)) {
				if (approvers.contains(currentUser)) {
					if (updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
						updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
					}
				}
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductionOrderDTO awaitingProductionOrderDto) throws ParseException {

//        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(awaitingProductionOrderDto.getCodexTorchDeleted())) {
            awaitingProductionOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = awaitingProductionOrderDto.getId();
		ProductionOrder entity = new ProductionOrder();
        BeanUtils.copyProperties(awaitingProductionOrderDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		entity.setEstimatedCompletionTime(sdf.parse(awaitingProductionOrderDto.getEstimatedCompletionTime()));
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			throw new ServiceException("工单主键不能为空");
		} else {
			awaitingProductionOrderMapper.updateById(entity);
		}
		TorchResponse response = new TorchResponse();
        ProductionOrderVO vo = new ProductionOrderVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionOrderVO> findAwaitingProductionOrder(String id) {
		ProductionOrderVO vo = new ProductionOrderVO();
		if (!HuatekTools.isEmpty(id)) {
			vo= awaitingProductionOrderMapper.selectProductionOrderById(id);
			ProductionOrderViewVO viewVO =new ProductionOrderViewVO();
			//处理试验项目
			if(!ObjectUtils.isEmpty(vo.getExperimentProject())){
				this.execuateExperimentProject(vo,viewVO);
				vo.setExperimentProject(viewVO.getExperimentProject());
			}

			//处理标准规范
			if(!ObjectUtils.isEmpty(vo.getStandardSpecificationId())){
				this.execuateStandardSpecification(vo,viewVO);
				vo.setStandardSpecifications(viewVO.getStandardSpecifications());
			}

			if(HuatekTools.isEmpty(vo)) {
				throw new ServiceException("查询失败");
			}
			//BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductionOrderVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionOrderViewVO> findAwaitingProductionOrderView(String id) {
		ProductionOrderViewVO vo = new ProductionOrderViewVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductionOrderVO ordervo= awaitingProductionOrderMapper.selectProductionOrderById(id);
			BeanUtils.copyProperties(ordervo,vo);
			this.execuateExperimentProject(ordervo,vo);
			//处理标准规范
			this.execuateStandardSpecification(ordervo,vo);
			ProductionOrder relateOrder= awaitingProductionOrderMapper.selectById(ordervo.getRelatedWorkOrder());
			if(!ObjectUtils.isEmpty(relateOrder))
				vo.setRelatedWorkOrder(relateOrder.getWorkOrderNumber());

//			vo.setReport();//TODO 查询报告
			//查询操作历史
			List<ProductionOrderOperationHistoryVO> histories = productionOrderOperationHistoryMapper.selectByProductOrder(ordervo.getId());
			vo.setOperatHistory(histories);
			//查询工单任务数据
			List<ProductionTaskViewVO> taskVOS = productionTaskService.selectProductionTaskByProductionOrder(ordervo.getWorkOrderNumber());
			vo.setExperimentProjects(taskVOS);
		}
		TorchResponse<ProductionOrderViewVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	private void execuateStandardSpecification(ProductionOrderVO ordervo, ProductionOrderViewVO vo) {
		if(StringUtils.isNotEmpty(ordervo.getStandardSpecificationId())){
			String[]  ids = ordervo.getStandardSpecificationId().split(",");
			List<StandardSpecification> list = new ArrayList<>();
			for(String id : ids){
				StandardSpecification standardSpecification = standardSpecificationMapper.selectById(id);
				list.add(standardSpecification);
			}
			vo.setStandardSpecifications(list);
		}
	}

	private void execuateExperimentProject(ProductionOrderVO ordervo, ProductionOrderViewVO vo) {
		if(!StringUtils.isEmpty(ordervo.getDpaTestProject())){
			String[] testProject = ordervo.getDpaTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_dpaTestProject");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
		if(!StringUtils.isEmpty(ordervo.getSpecialAnalysisTestProject())){
			String[] testProject = ordervo.getDpaTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_specialAnalysisTestProject");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
		if(!StringUtils.isEmpty(ordervo.getInspectionTestProject())){
			String[] testProject = ordervo.getDpaTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_inspectionTestProject");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
		if(!StringUtils.isEmpty(ordervo.getQualityConsistencyTestItems())){
			String[] testProject = ordervo.getDpaTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_qualityConsistencyTestItems");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProductionOrder> awaitingProductionOrderList = awaitingProductionOrderMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionOrder awaitingProductionOrder : awaitingProductionOrderList) {
            awaitingProductionOrder.setCodexTorchDeleted(Constant.DEFAULT_YES);
            awaitingProductionOrderMapper.updateById(awaitingProductionOrder);
        }
		//awaitingProductionOrderMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("orderNumber",awaitingProductionOrderMapper::selectOptionsByOrderNumber);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("entrustedUnit",awaitingProductionOrderMapper::selectOptionsByEntrustedUnit);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("productModel",awaitingProductionOrderMapper::selectOptionsByProductModel);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("predecessorWorkOrder",awaitingProductionOrderMapper::selectOptionsByPredecessorWorkOrder);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("relatedWorkOrder",awaitingProductionOrderMapper::selectOptionsByRelatedWorkOrder);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("responsiblePerson",awaitingProductionOrderMapper::selectOptionsByResponsiblePerson);
			selectOptionsFuncMap.put("processCode3",awaitingProductionOrderMapper::selectOptionsByProcessCode3);
//			selectOptionsFuncMap.put("assoWoPredProc",assoWoPredProc->awaitingProductionOrderMapper.selectOptionsByAssoWoPredProc(orderId));
			selectOptionsFuncMap.put("workstation",awaitingProductionOrderMapper::selectOptionsByWorkstation);
			selectOptionsFuncMap.put("deviceType",awaitingProductionOrderMapper::selectOptionsByDeviceType);
			selectOptionsFuncMap.put("productInformation1",awaitingProductionOrderMapper::selectOptionsByProductInformation1);
			selectOptionsFuncMap.put("testingTeam",awaitingProductionOrderMapper::selectOptionsByTestingTeam);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }
  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}

	public TorchResponse getRelateOrderPreProcess(String orderIds){
		Page<SelectOptionsVO>  selectOptionsVOs = awaitingProductionOrderMapper.selectOptionsByAssoWoPredProc(orderIds);
		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
		response.getData().setData(selectOptionsVOs);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount(selectOptionsVOs.getTotal());
		return response;
	}

	@Override
	public TorchResponse<ProductionOrderInfoVO> getDetailByOrderNumber(String productionWorkOrderNumber) {
		ProductionOrderInfoVO productionOrderInfoVO = awaitingProductionOrderMapper.getDetailByOrderNumber(productionWorkOrderNumber);
		TorchResponse<ProductionOrderInfoVO> response = new TorchResponse<>();
		response.getData().setData(productionOrderInfoVO);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional
	public TorchResponse approve(FormApprovalDTO formApprovalDTO, String token) {
		log.info("表单审批,formApprovalDTO:{}",formApprovalDTO);

		ProcessFormDTO processFormDTO = new ProcessFormDTO();
		BeanUtils.copyProperties(formApprovalDTO,processFormDTO);
		processFormDTO.setBusinessKey(processBusinessKey);
		SysProcessRecordVO processRecordResp = processInstanceProxyService.approve(processFormDTO,token);

		ProductionOrder updateProcessStatusEntity = new ProductionOrder();
		updateProcessStatusEntity.setId(processRecordResp.getFormId());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());

		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
		if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.PENDING_REAPPLY.getName()))
			updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT);
		if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.APPROVED.getName()))
			updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
		updateProcessStatusEntity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		updateProcessStatusEntity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(updateProcessStatusEntity);

		log.info("表单审批完成,processRecordResp:{}",processRecordResp);
		//审批通过后将该工单绑定的工序方案设置为可查询
		CustomerProcessScheme customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(updateProcessStatusEntity.getId());
		customerProcessScheme.setStatus(DicConstant.CommonDic.DIC_YES);
		customerProcessSchemeMapper.updateById(customerProcessScheme);
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse apply(ProductionOrderDTO awaitingProductionOrderDto, String token) {
		log.info("表单提交申请,ProductionOrderDto:{}",awaitingProductionOrderDto);
		ProductionOrder order = awaitingProductionOrderMapper.selectById(awaitingProductionOrderDto.getId());
		JSONObject json = securityUser.currentUser(token);
		String currentUser = json.getString("userName");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE);
		order.setPreparedBy(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchApplicant(currentUser);
		order.setCodexTorchApprovalStatus(ApprovalStatus.PENDING_APPROVAL.getName());
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		//CodeX 表单工作流绑定
		ProcessFormDTO processFormDTO = new ProcessFormDTO();
		processFormDTO.setProcessDefinitionKey("SimpleApprovalProcess");
		processFormDTO.setBusinessKey(processBusinessKey);
		processFormDTO.setFormId(awaitingProductionOrderDto.getId());
		SysProcessRecordVO processRecordResp = processInstanceProxyService.startProcessByKey(processFormDTO,token);
		ProductionOrder updateProcessStatusEntity = new ProductionOrder();
		updateProcessStatusEntity.setId(awaitingProductionOrderDto.getId());
		updateProcessStatusEntity.setCodexTorchApplicant(processRecordResp.getApplicant());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());

		//更新审批人列表
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());
//		updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE);
		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());

		awaitingProductionOrderMapper.updateById(updateProcessStatusEntity);
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
//		return updateVoResp;
	}

	@Override
	public TorchResponse getExperimentProjectByPlan(ExperimentProjectDTO dxperimentProjectDTO) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",dxperimentProjectDTO.getCodexTorchMasterFormId());
		List<ExperimentProject> list = experimentProjectMapper.selectList(wrapper);
		List<ExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			ExperimentProjectVO vo = new ExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessId());
			StandardProcessManagement assoWoPredstandardProcess =standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
				vo.setProcessCode3(vo.getProcessId());
			if(!ObjectUtils.isEmpty(assoWoPredstandardProcess))
				vo.setAssoWoPredProcName(assoWoPredstandardProcess.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}
	@Override
	public TorchResponse getExperimentProjectByCustomerPlan(CustomerExperimentProjectDTO customerExperimentProjectDTO) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",customerExperimentProjectDTO.getCodexTorchMasterFormId());
		List<CustomerExperimentProject> list = customerExperimentProjectMapper.selectList(wrapper);
		List<CustomerExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			CustomerExperimentProjectVO vo = new CustomerExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessCode3());
			StandardProcessManagement assoWoPredstandardProcess =standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(assoWoPredstandardProcess))
				vo.setAssoWoPredProcName(assoWoPredstandardProcess.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}

	@Override
	public TorchResponse getExperimentProjectByProcess(String[] ids) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("id",ids);
		List<StandardProcessManagement> list = standardProcessManagementMapper.selectList(wrapper);
		List<StandardProcessManagementVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			StandardProcessManagementVO vo = new StandardProcessManagementVO();
			BeanUtils.copyProperties(x,vo);
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			vo.setProcessCode3Name(x.getProcessName2());
			vo.setProcessCode3(x.getId());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}

	@Override
	public TorchResponse restoreProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE))
			throw new ServiceException("工单不是暂停状态不能进行恢复操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS);
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("暂停恢复");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse issueProductionOrderTask(ProductionOrderDTO productionOrderDTO) {
		List<ProductionTaskDTO> productionTaskList = new ArrayList<>();
		for(String id : productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)){
				throw new ServiceException("工单未审批通过或已下发");
			}
			ProductList productList = productListMapper.selectById(order.getProduct());
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("work_order",productionOrderDTO.getOrderIds()[0]);
			CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);
			CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();
			customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme.getId());
			List<CustomerExperimentProjectVO> customerEperimentProjects= customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);
			customerEperimentProjects.stream().forEach(x->{
				ProductionTaskDTO task = new ProductionTaskDTO();
				task.setWorkOrderNumber(order.getWorkOrderNumber());
				task.setInspectionQuantity2(order.getQuantity());
				task.setProcessName2(x.getProcessCode3Name());
				task.setProcessCode(x.getProcessCode3());
				task.setTestBasis(x.getTestBasis());
				task.setOrderNumber(order.getOrderNumber());
				task.setDisplayNumber(x.getDisplayNumber());
				task.setResponsiblePerson(order.getResponsiblePerson());
				task.setCustomerProcessName(x.getCustomerProcessName());
				task.setTestConditions(x.getTestConditions());
				task.setJudgmentCriteria(x.getJudgmentCriteria());
				task.setTicketLevel(productList.getTaskLevel());
				task.setScheduledStartTime(x.getEstimatedStartTime());
				task.setScheduledEndTime(x.getEstimatedEndTime());
				task.setProductName(productList.getProductName());
				task.setWorkstation(x.getWorkstation());
				task.setProductModel(productList.getProductModel());
				task.setProductCategory(productList.getProductCategory());
				task.setManufacturer(productList.getManufacturer());
				task.setBatchNumber(productList.getProductionBatch());
				task.setEntrustedUnit(scheme.getEntrustedUnit());
				task.setGrouping(x.getGrouping());
				task.setTestMethodology(x.getTestMethodology());
				task.setTestType(productList.getTestType());
				task.setExecutionSequence(x.getExecutionSequence());
				task.setProductInformation1(x.getProductInformation1());

				//获取是否审批
				StandardProcessManagement currentStandardProcessManagement = standardProcessManagementMapper.selectById(x.getProcessId());
				if(!ObjectUtils.isEmpty(currentStandardProcessManagement)){
					task.setIsApproval(currentStandardProcessManagement.getIsApproval());
				}
				task.setAssoWoPredProc(x.getAssoWoPredProc());
//				StandardProcessManagement standardProcessManagement = standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
//				if(!ObjectUtils.isEmpty(standardProcessManagement)){
//					task.setAssoWoPredProc(standardProcessManagement.getProcessName2());
//				}
				ProductionOrder relateOrder = awaitingProductionOrderMapper.selectById(order.getRelatedWorkOrder());
				if(!ObjectUtils.isEmpty(relateOrder))
					task.setRelatedWorkOrder(order.getRelatedWorkOrder());
				task.setBelongingTeam2(x.getTestingTeam());
				if(StringUtils.isNotEmpty(x.getTestingTeam())){
					TorchResponse<SysGroupVO> testTeam = sysGroupService.findGroup(x.getTestingTeam());
					TorchResponse<SysGroupVO> group =sysGroupService.findGroup(testTeam.getData().getData().getGroupParentId());
					task.setDepartment(group.getData().getData().getId());
				}
				productionTaskList.add(task);
			});
			//保存操作历史
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("任务下发");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
		}
		productionTaskService.saveBatch(productionTaskList);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse getBindProcessSchemePlan(ProductionOrderDTO productionOrderDTO) {
		CustomerProcessScheme  customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(productionOrderDTO.getId());
		if(ObjectUtils.isEmpty(customerProcessScheme)){
			TorchResponse response = new TorchResponse();
			response.setStatus(Constant.REQUEST_SUCCESS);
			return response;
		}
		CustomerProcessSchemeVO schemeVO = new CustomerProcessSchemeVO();
		schemeVO.setPda(customerProcessScheme.getPda());
		schemeVO.setPackageForm(customerProcessScheme.getPackageForm());
		schemeVO.setComment(customerProcessScheme.getComment());
		schemeVO.setReportComment(customerProcessScheme.getReportComment());
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",customerProcessScheme.getId());
		List<CustomerExperimentProject> list = customerExperimentProjectMapper.selectList(wrapper);
		List<CustomerExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			CustomerExperimentProjectVO vo = new CustomerExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessCode3());
			StandardProcessManagement assoWoPredstandardProcess =standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(assoWoPredstandardProcess))
				vo.setAssoWoPredProcName(assoWoPredstandardProcess.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		schemeVO.setCustomerExperimentProjectItems(voList);
		TorchResponse response = new TorchResponse();
		response.getData().setData(schemeVO);
		return response;
	}

	@Override
	public TorchResponse getStandProcessForCustomerPlan(StandardProcessManagementDTO standardProcessManagementDTO) {
		StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(standardProcessManagementDTO.getId());
		TorchResponse response = new TorchResponse();
		response.getData().setData(standardProcessManagement);
		return response;
	}

	@Override
	public TorchResponse copyProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		//查询当前工单号拆分的最后一个工单
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.like("work_order_number",order.getWorkOrderNumber().split("-")[0]);
		wrapper.orderByDesc("work_order_number");
		List<ProductionOrder> orderlist =awaitingProductionOrderMapper.selectList(wrapper);
		if(productionOrderDTO.getCopyType().equals(DicConstant.ProductionOrder.COPY_TYPE_SAME)){
			// 同级复制：只有带有后缀的工单才能进行同级复制
			if (!order.getWorkOrderNumber().contains("-")) {
				throw new ServiceException("基础工单不能进行同级复制，只能进行子级复制");
			}
			order.setWorkOrderNumber(generateSameLevelNumber(orderlist, order));
		}
		if(productionOrderDTO.getCopyType().equals(DicConstant.ProductionOrder.COPY_TYPE_CHILD)){
			// 子级复制：所有工单都可以进行子级复制
			order.setWorkOrderNumber(generateChildLevelNumber(orderlist, order));
		}
		order.setId(null);
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION);
		order.setCodexTorchApplicant(null);
		order.setCodexTorchApprovers(null);
		order.setCodexTorchApprovalStatus(null);
		order.setCodexTorchApprover(null);
		order.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.insert(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("复制");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}


	// 同级复制生成编号方法
	private String generateSameLevelNumber(List<ProductionOrder> orderList, ProductionOrder sourceOrder) {
		String sourceNumber = sourceOrder.getWorkOrderNumber();
		String[] sourceParts = sourceNumber.split("-");

		// 获取父级前缀（去掉最后一级数字）
		String parentPrefix = String.join("-", Arrays.copyOf(sourceParts, sourceParts.length - 1));

		// 查找同一父级下的最大同级编号
		int maxSibling = 0;
		for (ProductionOrder order : orderList) {
			String workOrderNumber = order.getWorkOrderNumber();
			if (workOrderNumber.startsWith(parentPrefix + "-")) {
				String[] parts = workOrderNumber.split("-");
				// 确保是同一层级（与源工单层级相同）
				if (parts.length == sourceParts.length) {
					try {
						int currentNum = Integer.parseInt(parts[parts.length - 1]);
						maxSibling = Math.max(maxSibling, currentNum);
					} catch (NumberFormatException e) {
						// 忽略非数字后缀
					}
				}
			}
		}

		return parentPrefix + "-" + (maxSibling + 1);
	}

	// 子级复制生成编号方法
	private String generateChildLevelNumber(List<ProductionOrder> orderList, ProductionOrder sourceOrder) {
		String sourceNumber = sourceOrder.getWorkOrderNumber();

		// 查找当前工单下的最大子级编号
		int maxChild = 0;
		String childPrefix = sourceNumber + "-";

		for (ProductionOrder order : orderList) {
			String workOrderNumber = order.getWorkOrderNumber();
			if (workOrderNumber.startsWith(childPrefix)) {
				String[] parts = workOrderNumber.split("-");
				// 确保是直接子级（比源工单多一级）
				if (parts.length == sourceNumber.split("-").length + 1) {
					try {
						int currentNum = Integer.parseInt(parts[parts.length - 1]);
						maxChild = Math.max(maxChild, currentNum);
					} catch (NumberFormatException e) {
						// 忽略非数字后缀
					}
				}
			}
		}

		return sourceNumber + "-" + (maxChild + 1);
	}
	@Override
	@Transactional
	public TorchResponse splitProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)){
			throw new ServiceException("工单未审批，无法拆分");
		}
		List<SplitOrderProductionTaskDTO> splitList = new ArrayList<>();
		//将工单的数量设置为原始工单数量
		order.setQuantity(productionOrderDTO.getOldQquantity());
		//查询当前工单号拆分的最后一个工单
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.like("work_order_number",order.getWorkOrderNumber());
		wrapper.orderByDesc("work_order_number");
		List<ProductionOrder> orderlist =awaitingProductionOrderMapper.selectList(wrapper);
		ProductionOrder lastOrder = orderlist.get(0);
		String lastOrderNumber = lastOrder.getWorkOrderNumber();
		String orderNumber="";
		if(lastOrderNumber.contains("-")){
			orderNumber=lastOrderNumber.split("-")[0]+"-"+(Integer.valueOf(lastOrderNumber.split("-")[1])+1);
		}else{
			orderNumber=lastOrderNumber.split("-")[0]+"-1";
		}
		SplitOrderProductionTaskDTO dto = new SplitOrderProductionTaskDTO();
		dto.setIsSplit(DicConstant.CommonDic.DEFAULT_ZERO);
		dto.setWorkOrderNumber(order.getWorkOrderNumber());
		dto.setInspectionQuantity(productionOrderDTO.getOldQquantity());
		splitList.add(dto);
		for(Integer num:productionOrderDTO.getSplitQuantity()){
			ProductionOrder newOrder = new ProductionOrder();
			BeanUtils.copyProperties(order,newOrder);
			newOrder.setId(null);
			newOrder.setQuantity(num);
			newOrder.setWorkOrderNumber(orderNumber);
			newOrder.setSoruceOrder(order.getWorkOrderNumber());
			awaitingProductionOrderMapper.insert(newOrder);
			orderNumber=orderNumber.split("-")[0]+"-"+(Integer.valueOf(orderNumber.split("-")[1])+1);
			//复制保存原工单绑定的方案
			CustomerProcessScheme  customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(order.getId());
			QueryWrapper wrapper1 = new QueryWrapper();
			wrapper1.eq("CODEX_TORCH_MASTER_FORM_ID",customerProcessScheme.getId());
			List<CustomerExperimentProject> listproject = customerExperimentProjectMapper.selectList(wrapper1);
			CustomerProcessScheme newScheme = new CustomerProcessScheme();
			BeanUtils.copyProperties(customerProcessScheme,newScheme);
			newScheme.setWorkOrder(newOrder.getId());
			newScheme.setId(null);
			customerProcessSchemeMapper.insert(newScheme);
			listproject.stream().forEach(x->{
				CustomerExperimentProject newproject = new CustomerExperimentProject();
				BeanUtils.copyProperties(x,newproject);
				newproject.setId(null);
				newproject.setCodexTorchMasterFormId(newScheme.getId());
				customerExperimentProjectMapper.insert(newproject);
				wrapper1.clear();
				wrapper1.eq("CODEX_TORCH_MASTER_FORM_ID",x.getId());
				List<CustomerExperimentProjectData> listprojectData =customerExperimentProjectDataMapper.selectList(wrapper1);
				listprojectData.stream().forEach(s->{
					CustomerExperimentProjectData newprojectdata = new CustomerExperimentProjectData();
					BeanUtils.copyProperties(s,newprojectdata);
					newprojectdata.setId(null);
					newprojectdata.setCodexTorchMasterFormId(newproject.getId());
					customerExperimentProjectDataMapper.insert(newprojectdata);
				});
			});

			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS)){
				//调用工单任务分单接口拆分工单任务
				SplitOrderProductionTaskDTO newdto = new SplitOrderProductionTaskDTO();
				newdto.setIsSplit(DicConstant.CommonDic.DEFAULT_ONE);
				newdto.setWorkOrderNumber(orderNumber);
				newdto.setInspectionQuantity(num);
				splitList.add(newdto);
			}
		}
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		productionTaskService.splitOrder(splitList);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("分单");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse cancelPdaWarning(ProductionOrderDTO productionOrderDTO) {
		for(String id :productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			order.setWmfcnr(DicConstant.CommonDic.DEFAULT_ZERO);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
			//保存操作历史
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("取消pda预警");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse pauseProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS))
			throw new ServiceException("订单状态不是进行中不能进行暂停操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("暂停");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse cancelProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE)
		)
			throw new ServiceException("订单处于取消、完成、已外协、待审批状态不能进行取消操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL);
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("取消");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse productionOrderInstore(ProductionOrderDTO productionOrderDTO) {
		for(String id : productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			ProductList productList = productListMapper.selectById(order.getProduct());
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
			CustomerInformationManagement customerInformationManagement= customerInformationManagementMapper.selectById(evaluationOrder.getCustomerId());
			order.setWhetherToEnterComponents(productionOrderDTO.getWhetherToEnterComponents());
			order.setWhetherToEnterDocuments(productionOrderDTO.getWhetherToEnterDocuments());
			//TODO 调用入库接口保存入库数据，组装入库DTO
			ProductInventoryDTO dto = new ProductInventoryDTO();
			dto.setProductModel(productList.getProductModel());
			dto.setWorkOrderNumber(order.getWorkOrderNumber());
			dto.setProductName(productList.getProductName());
			dto.setEntrustedUnit(customerInformationManagement.getEntrustedUnit());
			dto.setBatch(productList.getProductionBatch());
			dto.setQuantityOfCommissionedItems(productList.getSampleTotalCount());
			dto.setQuantityOfScreenedItems(order.getQuantity());
			dto.setStatus(DicConstant.CommonDic.DEFAULT_ZERO);
			dto.setEngineeringCode(evaluationOrder.getEngineeringCode());
			dto.setInspectionSender(evaluationOrder.getPrincipal());
			if(productionOrderDTO.getWhetherToEnterDocuments().equals(DicConstant.CommonDic.DEFAULT_ONE)){
				dto.setReport(DicConstant.CommonDic.DEFAULT_ONE);
				//TODO 根据工单查询报告编号
//			dto.setReportNumber();
				ProductionOrderResultVO resultVO = productionOrderResultService.selectResultByProductionOrder(order.getId());
				if(!ObjectUtils.isEmpty(resultVO))
					dto.setUnqualifiedQuantity(resultVO.getUnqualifiedQuantity());//不合格数量
				TorchResponse<List<UnqualifiedProcessVO>> response = productionTaskService.getUnqualifiedTaskInfo(order.getWorkOrderNumber());
				TorchResponse<Long> qualifiedQuantiryResponse =  productionTaskService.getLastProcessQualifiedQuantity(order.getWorkOrderNumber());
				if(!ObjectUtils.isEmpty(response.getData().getData())){
					String process = String.join(",",response.getData().getData().stream().map(UnqualifiedProcessVO::getProcessName).collect(Collectors.toList()));
					String reson = String.join(",",response.getData().getData().stream().map(UnqualifiedProcessVO::getFailureMode).collect(Collectors.toList()));
					dto.setNonQualityProcess(process);
					dto.setReasonForNonQuality(reson);
				}
				if(!ObjectUtils.isEmpty(qualifiedQuantiryResponse.getData().getData()))
					dto.setQualifiedQuantity(qualifiedQuantiryResponse.getData().getData().intValue());

			}
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			order.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_INSTORE);
			awaitingProductionOrderMapper.updateById(order);
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("入库");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
			productInventoryService.saveOrUpdate(dto);
		}

		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse connotScreened(ProductionOrderDTO productionOrderDTO) {
		for(String id :productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			order.setIrretrievableReason(productionOrderDTO.getIrretrievableReason());
			order.setNonAgingReason(productionOrderDTO.getNonAgingReason());
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL);
			order.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_CANNOT_SCREENED);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
			//保存操作历史
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("不可筛");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
		}

		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse outSorucingBack(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_REJECT)){
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
		}
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_APPROVED)){
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED);
		}
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_ACCEPTED)){
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)){
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
			}
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)){
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE);
			}
		}
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse outSourcingApply(AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO) {
		for(String id :addOrUpdateOutsourcingDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)||
					order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)
					||order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION)
			){
				addOrUpdateOutsourcingDTO.setEntireOrProcess(DicConstant.CommonDic.DEFAULT_ZERO);
				addOrUpdateOutsourcingDTO.setOrderId(id);
				outsourcingService.saveOrUpdate(addOrUpdateOutsourcingDTO);
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
				order.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING);
				order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
				order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
				QueryWrapper wrapper = new QueryWrapper();
				wrapper.eq("id",order.getId());
				awaitingProductionOrderMapper.update(order,wrapper);
				//保存操作历史
				ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
				history.setProductionOrder(addOrUpdateOutsourcingDTO.getOrderId());
				history.setOperate("外协申请");
				history.setOperator(SecurityContextHolder.getCurrentUserId());
				history.setOperateTime(new Timestamp(System.currentTimeMillis()));
				productionOrderOperationHistoryMapper.insert(history);
			}else{
				throw new ServiceException("订单不是草稿或审批通过状态不能进行外协申请");
			}
		}

		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}


	@Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "evaluation_order":
                    data = selectDataLinkageByOrderNumber(conditionalValue);
                    break;
                case "product_list1":
                    data = selectDataLinkageByProductModel(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByOrderNumber(String order_number) {
        return awaitingProductionOrderMapper.selectDataLinkageByOrderNumber(order_number);
    }
    @Override
    public Map<String,String> selectDataLinkageByProductModel(String product_model) {
        return awaitingProductionOrderMapper.selectDataLinkageByProductModel(product_model);
    }

    @Override
    @ExcelExportConversion(tableName = "production_order", convertorFields = "reportRequirements,reportFormat,dataReqERep,dataReqsPapereport,testMethodology,whetherToIncludeInScheduling,workOrderStatus,wtstabr,whetherToEnterComponents,whetherToEnterDocuments,wmfcnr")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionOrderVO> selectAwaitingProductionOrderList(ProductionOrderDTO dto) {
        return awaitingProductionOrderMapper.selectAwaitingProductionOrderList(dto);
    }

    @Override
    public TorchResponse selectAwaitingProductionOrderListByIds(List<String> ids) {
        List<ProductionOrderVO> awaitingProductionOrderList = awaitingProductionOrderMapper.selectAwaitingProductionOrderListByIds(ids);

		TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
		response.getData().setData(awaitingProductionOrderList);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount((long)awaitingProductionOrderList.size());
		return response;
    }
	@Override
	@Transactional
	public TorchResponse issuedProductByIds(List<String> ids) {
		if (CollUtil.isEmpty(ids)){
			TorchResponse resp = new TorchResponse();
			resp.getData().setData(new ArrayList<ProductionOrder>());
			return resp;
		}
		//根据产品列表id查询产品信息
		List<ProductListVO> productList = productListMapper.selectProductListListByIds(ids);
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("product",ids);
		List<ProductionOrder> productionOrders = awaitingProductionOrderMapper.selectList(wrapper);
		if(!ObjectUtils.isEmpty(productionOrders) ||productionOrders.size()>0 ){
			throw new ServiceException("存在已下发产品，请勿重复下发");
		}
		List<ProductionOrder> list = new ArrayList<>();
		List<ProductionOrder> result = new ArrayList<>();
		Map<String,String> workordernumMap = new HashMap<>();
		for(ProductListVO vo : productList){
			//查询测评订单信息
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(vo.getEvaluationOrderId());

			String workOrder = "";
			//判断下发的数据中是否有同型号同批次
//			workordernumMap.put(vo.getProductModel(),"");
			workOrder =this.getProductionNumber(vo,workordernumMap);
			ProductionOrder productionOrder = new ProductionOrder();
			productionOrder.setWorkOrderNumber(workOrder);
			productionOrder.setOrderNumber(evaluationOrder.getOrderNumber());
			productionOrder.setProduct(vo.getId());
			productionOrder.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_SELF);
			productionOrder.setQuantity(vo.getQuantity());
			productionOrder.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION);
			productionOrder.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_NOSTARTED);
			productionOrder.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			productionOrder.setCodexTorchGroupId(vo.getCodexTorchGroupId());
			productionOrder.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.insert(productionOrder);
			if(workOrder.equals(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
				list.add(productionOrder);
			}
			result.add(productionOrder);
		}
		fileReviewService.issueProductCreateFileVeiw(list);
		TorchResponse<List<ProductionOrder>> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(result);
		return response;
	}

	@Override
	public TorchResponse setResponsiblePerson(ProductionOrderDTO awaitingProductionOrderDto) {
		for(String id :awaitingProductionOrderDto.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			order.setResponsiblePerson(awaitingProductionOrderDto.getResponsiblePerson());
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
			productionOrderOperationHistory.setProductionOrder(order.getId());
			productionOrderOperationHistory.setOperate("指派负责人");
			productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
			productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
			productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse judgeModelAndBatch(ProductionOrderDTO awaitingProductionOrderDto) {
		String orderNumbers ="";
		for(String id : awaitingProductionOrderDto.getOrderIds()){
			SysGroup groupKekao = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_KEKAOXING);
			//查询当前工单
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			ProductList productList = productListMapper.selectById(order.getProduct());
			if(!order.getCodexTorchGroupId().equals(groupKekao.getId())){
				TorchResponse response = new TorchResponse<>();
				response.setStatus(Constant.REQUEST_SUCCESS);
				return response;
			}
			List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productList.getProductModel(),
					productList.getProductionBatch(),productList.getManufacturer(),order.getCodexTorchGroupId(), id);
			if(!ObjectUtils.isEmpty(existOrder) && existOrder.size() > 0){
				if(orderNumbers.equals("")){
					orderNumbers+=order.getWorkOrderNumber()+",";
				}else{
					orderNumbers+=order.getWorkOrderNumber();
				}
			}
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(orderNumbers);
		return response;
	}

	private String getSameModeBatchManufacture(String productMode,String productBatch,String manufacturer,String groupId,String currentProductionOrderId){
		List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productMode,
				productBatch,manufacturer,groupId, currentProductionOrderId);
		String flag=DicConstant.CommonDic.DEFAULT_ZERO;
		if(existOrder.size()>1)flag=DicConstant.CommonDic.DEFAULT_ONE;
		return flag;
	};
	@Override
	public TorchResponse SaveWtstabr(ProductionOrderDTO dto) {
		for(String id : dto.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			ProductList productList = productListMapper.selectById(order.getProduct());
			List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productList.getProductModel(),
					productList.getProductionBatch(),productList.getManufacturer(),order.getCodexTorchGroupId(), id);
			if(!ObjectUtils.isEmpty(existOrder) && existOrder.size()>0){
				order.setWtstabr(dto.getWtstabr());
				awaitingProductionOrderMapper.updateById(order);
			}
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional
	public TorchResponse saveCustomerProcessSheme(CustomerProcessSchemeDTO customerProcessSchemeDTO, String token) {
		//判断工单状态是否符合要求，
		QueryWrapper wrapper = new QueryWrapper<>();
		wrapper.in("id",customerProcessSchemeDTO.getOrderIds());
		List<ProductionOrder> orders = awaitingProductionOrderMapper.selectList(wrapper);
		List<String> orderStatus = orders.stream().map(ProductionOrder::getWorkOrderStatus).distinct().collect(Collectors.toList());
		if(orderStatus.stream().anyMatch(status -> !Arrays.asList(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT, DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT,
				DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION).contains(status))){
			throw new ServiceException("除草稿、待提交、驳回状态的工单才能绑定工序方案");
		}
		for(String id : customerProcessSchemeDTO.getOrderIds()){
			//查询工单信息
			ProductionOrder order= awaitingProductionOrderMapper.selectById(id);
			//如果是提交,需要判断
			if(customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
				boolean result = capabilityReviewService.isReviewOkByPrioductListId(order.getProduct());
				if(!result){//评审未通过
					TorchResponse response = new TorchResponse<>();
					response.setStatus(Constant.REQUEST_SUCCESS);
					response.setMessage("能力评审未通过,不能提交");
					return response;
				}
				//提交审批
				ProductionOrderDTO dto = new ProductionOrderDTO();
				BeanUtils.copyProperties(order,dto);
				this.apply(dto,token);
			}

			ProductList productList =productListMapper.selectById(order.getProduct());
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());

			//判断工单是否已绑定方案如果已绑定，将原有方案删除新增
			CustomerProcessScheme scheme =customerProcessSchemeMapper.selectByProductionOrder(id);
			if(ObjectUtils.isEmpty(scheme)){
				scheme = new CustomerProcessScheme();
				scheme.setWorkOrder(id);
				scheme.setTestType(productList.getTestType());
				scheme.setPda(customerProcessSchemeDTO.getPda());
				scheme.setPackageForm(customerProcessSchemeDTO.getPackageForm());
				scheme.setEntrustedUnit(evaluationOrder.getCustomerId());
				scheme.setStandardSpecificationNumber(productList.getStandardSpecificationId());
				scheme.setManufacturer(productList.getManufacturer());
				scheme.setQualityGrade(productList.getQualityGrade());
				scheme.setPda(order.getPda());
				if(customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
					scheme.setStatus(DicConstant.CommonDic.DIC_YES);
				}else{
					scheme.setStatus(DicConstant.CommonDic.DIC_NO);
				}
				//查询产品管理id
				wrapper.clear();
				wrapper.eq("product_model",productList.getProductModel());
				wrapper.eq("product_name",productList.getProductName());
				wrapper.eq("manufacturer",productList.getManufacturer());
				ProductManagement productManagement =productManagementMapper.selectOne(wrapper);
				if(!ObjectUtils.isEmpty(productManagement))
					scheme.setProductModel(productManagement.getId());
				scheme.setPackageForm(order.getPackageForm());

//		scheme.setTestPackage(productList.gettes);
				scheme.setComment(customerProcessSchemeDTO.getComment());
				scheme.setReportComment(customerProcessSchemeDTO.getReportComment());
				scheme.setDepartment(order.getCodexTorchGroupId());
				scheme.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
				scheme.setCodexTorchCreatorId(order.getCodexTorchGroupId());
				scheme.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
				customerProcessSchemeMapper.insert(scheme);
			}else{
				scheme.setPda(customerProcessSchemeDTO.getPda());
				scheme.setPackageForm(customerProcessSchemeDTO.getPackageForm());
				scheme.setComment(customerProcessSchemeDTO.getComment());
				scheme.setReportComment(customerProcessSchemeDTO.getReportComment());
				customerProcessSchemeMapper.updateById(scheme);
				wrapper.clear();
				wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme.getId());
				List<CustomerExperimentProject> projects= customerExperimentProjectMapper.selectList(wrapper);
				wrapper.clear();
				List<String> ids = projects.stream().map(CustomerExperimentProject::getId).collect(Collectors.toList());
				if(!ObjectUtils.isEmpty(ids)){
					wrapper.in("CODEX_TORCH_MASTER_FORM_ID",ids);
					customerExperimentProjectDataMapper.delete(wrapper);
					wrapper.clear();
					wrapper.in("id",ids);
					customerExperimentProjectMapper.delete(wrapper);
				}
			}
			//保存试验项目
			for(CustomerExperimentProjectDTO dto:customerProcessSchemeDTO.getDetailFormItems()){
				CustomerExperimentProject customerExperimentProject = new CustomerExperimentProject();
				BeanUtils.copyProperties(dto,customerExperimentProject);
				customerExperimentProject.setId(null);
				//非必要字段处理
				customerExperimentProject.setCodexTorchDeleted(Constant.DEFAULT_NO);
				//主子表关联ID
				customerExperimentProject.setCodexTorchMasterFormId(scheme.getId());
				customerExperimentProjectMapper.insert(customerExperimentProject);
				CustomerExperimentProjectDataDTO[] list = dto.getProjectDataItems();
				if(null!=list){
					for (CustomerExperimentProjectDataDTO dataDTO : dto.getProjectDataItems()){
						CustomerExperimentProjectData customerExperimentProjectData = new CustomerExperimentProjectData();
						BeanUtils.copyProperties(dataDTO,customerExperimentProjectData);
						customerExperimentProjectData.setId(null);
						customerExperimentProjectData.setCodexTorchDeleted(Constant.DEFAULT_NO);
						//主子表关联ID
						customerExperimentProjectData.setCodexTorchMasterFormId(customerExperimentProject.getId());
						customerExperimentProjectDataMapper.insert(customerExperimentProjectData);
					}
				}
			}
			ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
			productionOrderOperationHistory.setProductionOrder(order.getId());
			productionOrderOperationHistory.setOperate("绑定工序方案");
			productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
			productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);
			if(!customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
				order.setPreparedBy(SecurityContextHolder.getCurrentUserId());
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT);
				order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
				order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
				awaitingProductionOrderMapper.updateById(order);
			}
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}
	private String getProductionNumber(ProductListVO vo, Map<String, String> workordernumMap){
		SysGroup groupKekao = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_KEKAOXING);
		SysGroup groupShengchan = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_SHENGCHAN);
		String workOrder = "";

		// 鉴定试验和质量一致性试验特殊处理
		if (vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)) {
			TorchResponse response = codeManagementService.getOrderNumber(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY);
			workOrder = response.getData().getData().toString() + "-1";
			vo.setCodexTorchGroupId(groupKekao.getId());
			return workOrder;
		}

		// 确定工单前缀和所属组别
		String prefix = "";
		if (vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST)) {
			prefix = DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY;
		} else if (vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ONE)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_TWO)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ENVIRONMENT)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_REINSPECTION)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_MICROWAVE_QUALIFICATION_TEST)) {
			prefix = DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING;
		}

		// 使用 "前缀+产品型号" 作为key，确保K和S类型独立编号
		String productModelKey = prefix + vo.getProductModel();

		// 查询是否有同型号同前缀的工单
		ProductionOrder oldProductionOrder = awaitingProductionOrderMapper.selectProductionOrderByModelAndBatch(
				vo.getEvaluationOrderId(), vo.getProductModel(),prefix);

		// 如果已有同型号同前缀工单，记录到map中
		if (!ObjectUtils.isEmpty(oldProductionOrder) && oldProductionOrder.getWorkOrderNumber().startsWith(prefix)) {
			workordernumMap.put(productModelKey, oldProductionOrder.getWorkOrderNumber());
		}

		// 如果map中没有该型号该前缀的记录，生成新工单号
		if (workordernumMap.size() == 0 || !workordernumMap.containsKey(productModelKey)
				|| StringUtils.isEmpty(workordernumMap.get(productModelKey))) {

			TorchResponse response = codeManagementService.getOrderNumber(prefix);
			workOrder = response.getData().getData().toString();
			workordernumMap.put(productModelKey, workOrder);

			// 设置组别
			if ( DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY.equals(prefix)) {
				vo.setCodexTorchGroupId(groupKekao.getId());
			} else if ( DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING.equals(prefix)) {
				vo.setCodexTorchGroupId(groupShengchan.getId());
			}
		} else {
			// 已有同型号同前缀工单，生成连续编号
			String existingWorkOrder = workordernumMap.get(productModelKey);
			String[] nums = existingWorkOrder.split("-");

			if (nums.length == 1) {
				workOrder = nums[0] + "-1";
			} else {
				int sequence = Integer.valueOf(nums[1]) + 1;
				workOrder = nums[0] + "-" + sequence;
			}
			workordernumMap.put(productModelKey, workOrder);
			// 设置组别基于工单前缀
			if (workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)) {
				vo.setCodexTorchGroupId(groupKekao.getId());
			} else if (workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)) {
				vo.setCodexTorchGroupId(groupShengchan.getId());
			}
		}

		return workOrder;
//			if(vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)
//		|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)){
//			TorchResponse response = codeManagementService.getOrderNumber("K");
//			workOrder=response.getData().getData().toString()+"-1";
//		}else{
//			//查询工单中是否有同型号同批次的工单，如果有则工单编号为原工单加上“-N”
//			ProductionOrder oldproductionOrder = awaitingProductionOrderMapper.selectProductionOrderByModelAndBatch(vo.getEvaluationOrderId(),vo.getProductModel());
//			if(!ObjectUtils.isEmpty(oldproductionOrder))
//				workordernumMap.put(vo.getProductModel()+vo.getTestType(),oldproductionOrder.getWorkOrderNumber());
//			if(workordernumMap.size()==0 || workordernumMap.get(vo.getProductModel()).equals("")){
//				//根据试验类型生成工单号-K
//				if(vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST)){
//					TorchResponse response = codeManagementService.getOrderNumber("K");
//					workOrder=response.getData().getData().toString();
//					vo.setCodexTorchGroupId(groupKekao.getId());
//				}
//				//根据试验类型生成工单号-S
//				if(vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ONE)||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_TWO)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ENVIRONMENT)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_REINSPECTION)
//						||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_MICROWAVE_QUALIFICATION_TEST)){
//					TorchResponse response = codeManagementService.getOrderNumber("S");
//					workOrder=response.getData().getData().toString();
//					vo.setCodexTorchGroupId(groupShengchan.getId());
//				}
//				workordernumMap.put(vo.getProductModel(),workOrder);
//			}else{
//				String[] nums = workordernumMap.get(vo.getProductModel()).split("-");
//				if(nums.length==1){
//					workOrder=nums[0]+"-1";
//				}
//				if(nums.length>1){
//					workOrder=nums[0]+"-"+String.valueOf(Integer.valueOf(nums[1])+1);
//				}
//				workordernumMap.put(vo.getProductModel(),workOrder);
//				if(workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)){
//					vo.setCodexTorchGroupId(groupKekao.getId());
//				}
//				if(workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
//					vo.setCodexTorchGroupId(groupShengchan.getId());
//				}
//			}
//		}

	}


	@Override
	public TorchResponse completeProductionOrder(String workOrderNumber){
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order_number",workOrderNumber);
		ProductionOrder order = awaitingProductionOrderMapper.selectOne(wrapper);
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE);
		order.setCompletionTime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}
	@Override
	public TorchResponse updateProductionOrdrePdaWarning(ProductionOrderDTO dto){
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order_number",dto.getWorkOrderNumber());
		ProductionOrder order = awaitingProductionOrderMapper.selectOne(wrapper);
		if(order.getPda()!=null && order.getPda().compareTo(dto.getPda())<0){
			order.setWtstabr(DicConstant.CommonDic.DEFAULT_ONE);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

    @Override
    public TorchResponse<AbnormalfeedbackVO> getInfoByOrderNumber(String orderNumber) {
        AbnormalfeedbackVO productionOrder = awaitingProductionOrderMapper.getInfoByOrderNumber(orderNumber);

		TorchResponse<AbnormalfeedbackVO> response = new TorchResponse<>();
		response.getData().setData(productionOrder);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

	/**
	 * 1,测筛有合合并工单报告(可以多选）
	 * 2，可靠性只能选单挑数据（但鉴定和质量一致性会自动合并）
	 * @param productionOrderResultDTO
	 * @return
	 * @throws IOException
	 */
	@Override
	@Transactional
	public TorchResponse createProductionOrderReport(ProductionOrderDTO productionOrderResultDTO) throws IOException {
		//查询工单的报告状态,如果报告状态不是空或者草稿和驳回状态则不能生成报告
		List<ProductionOrderVO> productionOrders = awaitingProductionOrderMapper.selectAwaitingProductionOrderListByIds(Arrays.asList(productionOrderResultDTO.getOrderIds()));
		List<String> reportStatuses = productionOrders.stream().map(ProductionOrderVO::getReportStatus).collect(Collectors.toList());
		for(String status : reportStatuses){
			if(StringUtils.isNotBlank(status) && !status.equals(DicConstant.CommonDic.DRAFT) && !status.equals(DicConstant.CommonDic.REJECT)){
				throw new ServiceException("工单报告已生成或工单报告状态不是草稿和驳回状态则不能生成报告!");
			}
		}
		List<String> workOrders = productionOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.toList());
		boolean hasKStart = workOrders.stream().filter(workOrder -> workOrder != null).anyMatch(workOrder -> workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY));
		if(hasKStart && productionOrderResultDTO.getOrderIds().length>1){
			throw new ServiceException("可靠性工单不能合并生成报告!");
		}
		List<String> orderNumbers = productionOrders.stream().map(ProductionOrderVO::getOrderNumber).distinct().collect(Collectors.toList());
		List<String> models = productionOrders.stream().map(ProductionOrderVO::getProductModel).distinct().collect(Collectors.toList());
		List<String> batchs = productionOrders.stream().map(ProductionOrderVO::getProductionBatch).distinct().collect(Collectors.toList());
		List<String> testTyes = productionOrders.stream().map(ProductionOrderVO::getTestType).distinct().collect(Collectors.toList());

		if(orderNumbers.size()>1 || models.size()>1 || batchs.size()>1 || testTyes.size()>1){
			throw new ServiceException("选中的工单不是同订单、同型号、同批次、同试验类型不能合并生成报告!");
		}

		ReportDocxTool rt = new ReportDocxTool();
		//判断生成类型
		if(productionOrders.get(0).getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
			//测筛工单
			ProductionOrderReportVO orderReportVO = this.getReprotData(productionOrders);
			//生成报告
			JSONObject jsonData = TempData.createTempData();
			XWPFDocument document = rt.createDOCXDocument("D:/资料/君信/报表工具/table_temp_1.docx", jsonData);
			//rt.writeDOCX("D:/资料/君信/生产部报告.docx", JSONObject.parseObject(JSONObject.toJSONString(orderReportVO)),"D:/资料/君信/table_" + System.currentTimeMillis() + ".docx");
			//上传
			TorchResponse response = commonFileService.uploadFile(DocumentUtils.convertToMultipartFile(document,"table_" + System.currentTimeMillis() + ".docx"));

			//保存报告
			ReportManagement entity = new ReportManagement();
			entity.setWorkOrderNumber(productionOrders.get(0).getWorkOrderNumber());
			TorchResponse reportResponse = codeManagementService.getOrderNumber("GDBG");
			entity.setReportNumber(reportResponse.getData().getData().toString());
			entity.setCompilationTime(new Timestamp(System.currentTimeMillis()));
			entity.setPreparerOfTheReport(SecurityContextHolder.getCurrentUserId());
			entity.setReportStatus(DicConstant.CommonDic.DRAFT);
			entity.setAttachment(response.getData().getData().toString());
			entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			entity.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
			reportManagementMapper.insert(entity);
		}
		//可靠性工单,DPA报告
		if(productionOrders.get(0).getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)
		 && productionOrders.get(0).getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)){
			JSONObject jsonData = this.getDpaReportData(productionOrders.get(0));
			System.out.println(JSON.toJSONString(jsonData));
			rt.writeDOCX("D:/资料/君信/可靠性DPA分析报告.docx", jsonData,"D:/资料/君信/DPA_" + System.currentTimeMillis() + ".docx");
//			rt.createDOCXDocument("D:/资料/君信/可靠性DPA分析报告.docx", jsonData);
			XWPFDocument document = rt.createDOCXDocument("D:/资料/君信/可靠性DPA分析报告.docx", jsonData);
			//上传
			TorchResponse response = commonFileService.uploadFile(DocumentUtils.convertToMultipartFile(document,"table_" + System.currentTimeMillis() + ".docx"));

			//保存报告
			ReportManagement entity = new ReportManagement();
			entity.setWorkOrderNumber(productionOrders.get(0).getWorkOrderNumber());
			TorchResponse reportResponse = codeManagementService.getOrderNumber("GDBG");
			entity.setReportNumber(reportResponse.getData().getData().toString());
			entity.setCompilationTime(new Timestamp(System.currentTimeMillis()));
			entity.setPreparerOfTheReport(SecurityContextHolder.getCurrentUserId());
			entity.setReportStatus(DicConstant.CommonDic.DRAFT);
			entity.setAttachment(response.getData().getData().toString());
			entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			entity.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
			reportManagementMapper.insert(entity);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	private JSONObject getDpaReportData(ProductionOrderVO productionOrder) {
		JSONObject jsonObject = new JSONObject();
		String workOrderNumber = productionOrder.getWorkOrderNumber();
//		List<String> workOrderNumbers = productionOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.toList());
//		Collections.sort(workOrderNumbers);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order", workOrderNumber);
		ProductionOrderResult ordertestResult = productionOrderResultMapper.selectOne(wrapper);
		List<ProductionOrderProcessTest> tests = productionOrderProcessTestMapper.selectList(wrapper);
		//第一页数据
		jsonObject.put("entrustedUnit", productionOrder.getEntrustedUnit());
		jsonObject.put("productModel", productionOrder.getProductModel());
		jsonObject.put("productName", productionOrder.getProductName());
		jsonObject.put("manufacturer", productionOrder.getManufacturer());
		jsonObject.put("analyticalForm", "DPA分析");
		if(!ObjectUtils.isEmpty(ordertestResult))
		jsonObject.put("analysisConclusion", ordertestResult.getInspectionResult());
		//第二页数据
		jsonObject.put("workOrderNumber", workOrderNumber);
		//第三页数据
		//查询当前登录人的签名
		JSONObject image= new JSONObject();
		TorchResponse<SysUserVO> userResponse = sysUserService.findUser(SecurityContextHolder.getCurrentUserId());
		if(StringUtils.isNotBlank(userResponse.getData().getData().getElectronicSignature())) {
			image.put("dataType", "image");
			image.put("content", readImage(userResponse.getData().getData().getElectronicSignature()));
			image.put("w", 80);
			image.put("h", 50);

		}
		jsonObject.put("preparedBy", image);
//		jsonObject.put("preparedBy", "admin");
		jsonObject.put("preparedDate", sdf.format(new Date()));
//		jsonObject.put("reviewer", SecurityContextHolder.getCurrentUserName());
//		jsonObject.put("reviewDate", sdf.format(new Date()));
//		jsonObject.put("approver", SecurityContextHolder.getCurrentUserName());
//		jsonObject.put("approveDate", sdf.format(new Date()));
		//第四页数据
		//查询工单的提交时间
		List<String> orderIds = new ArrayList<>();
		orderIds.add(productionOrder.getId());
		List<SysProcessRecord> processRecodes = sysProcessRecordService.selectListByFromId(orderIds);
		Optional<String> earliestTime = processRecodes.stream()
				.map(SysProcessRecord::getCreateTime)
				.filter(Objects::nonNull)
				.min(Comparator.naturalOrder())
				.map(timestamp -> {
					// 使用 DateTimeFormatter 进行格式化
					return timestamp.toLocalDateTime()
							.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
				});
		String lastTime ="";
		if(!ObjectUtils.isEmpty(productionOrder.getCompletionTime()))
		lastTime = productionOrder.getCompletionTime().toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
		String analysisOverview="受"+productionOrder.getEntrustedUnit()+"委托，对"+productionOrder.getProductModel()
				+"型"+productionOrder.getProductName()+"进行DPA分析，本次分析从"+earliestTime.get()+"开始至"+lastTime+"结束";
		jsonObject.put("analysisOverview", analysisOverview);
		//查询所有工序对应的产品资料或标准规范
		Map<String, List<String>> groupedMap = tests.stream()
				.collect(Collectors.groupingBy(ProductionOrderProcessTest::getWorkOrder,
						Collectors.mapping(ProductionOrderProcessTest::getProcessCode,
								Collectors.toList())));
		List<SelectOptionsVO> options= customerExperimentProjectMapper.selectStandardSpecificationByProcessCodes(productionOrder.getId(),groupedMap.get(workOrderNumber));

		List<SelectOptionsVO> distinctOptions = options.stream()
				.collect(Collectors.toMap(
						vo -> vo.getValue() + "|" + vo.getLabel(),
						vo -> vo,
						(existing, replacement) -> existing
				)).values().stream().collect(Collectors.toList());
		String basisOfAnalysis="";
		for(int i=0;i<distinctOptions.size();i++){
			basisOfAnalysis+=i+1+"."+distinctOptions.get(i).getValue()+" "+distinctOptions.get(i).getLabel()+"\n";
		}
		jsonObject.put("basisOfAnalysis", basisOfAnalysis);
		jsonObject.put("productionBatch", productionOrder.getProductionBatch()==null?"/":productionOrder.getProductionBatch());
		//查询工单对应产品中的样品数量和样品总数，质量等级，
		ProductList productList = productListMapper.selectById(productionOrder.getProductListId());
		jsonObject.put("samplingSize", productList.getQuantity());
		jsonObject.put("totalSampleSize", productList.getSampleTotalCount());
		jsonObject.put("qualityGrades", productList.getQualityGrade());
		jsonObject.put("sampleSoruce", productList.getSampleTotalCount()>productList.getQuantity()?"随机抽样":"客户送样");
		jsonObject.put("analysisConclusion", ordertestResult.getInspectionConclusion());
		jsonObject.put("otherRemark", ordertestResult.getOthereExplanationRemark()==null?"/":ordertestResult.getOthereExplanationRemark());
		SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月dd日");
		jsonObject.put("yearMonthDay",sf.format(new Date()));
		//第五页数据
		JSONObject tab = new JSONObject();
		JSONArray thead = new JSONArray();
		JSONArray tbody = new JSONArray();
		tab.put("dataType", "table");
		tab.put("thead", thead);
		tab.put("tbody", tbody);
		JSONObject th1 = new JSONObject(); th1.put("name", "序号"); th1.put("code", "index"); thead.add(th1);
		JSONObject th2 = new JSONObject(); th2.put("name", "分析项目"); th2.put("code", "processName"); thead.add(th2);
		JSONObject th3 = new JSONObject(); th3.put("name", "样品数"); th3.put("code", "quality"); thead.add(th3);
		JSONObject th4 = new JSONObject(); th4.put("name", "合格数"); th4.put("code", "qualifiedQuantity"); thead.add(th4);
		JSONObject th5 = new JSONObject(); th5.put("name", "不合格数"); th5.put("code", "unqualifiedQuantity"); thead.add(th5);
		JSONObject th6 = new JSONObject(); th6.put("name", "备注"); th6.put("code", "reportWorkRemarks"); thead.add(th6);
		List<String> processDates = tests.stream().map(ProductionOrderProcessTest::getProcessData).collect(Collectors.toList());
		List<ProductionTaskVO> taskVOS = new ArrayList<>();
		processDates.stream().forEach(p->{
			taskVOS.add(JSON.parseObject(p, ProductionTaskVO.class));
		});
		taskVOS.stream().forEach(t->{
			JSONObject tr1 = new JSONObject(); tr1.put("index", t.getDisplayNumber()); tr1.put("processName", t.getProcessName2());
			tr1.put("quality", t.getInspectionQuantity2()); tr1.put("qualifiedQuantity", t.getQualifiedQuantity());
			tr1.put("unqualifiedQuantity", t.getUnqualifiedQuantity());
			tr1.put("reportWorkRemarks", t.getReportWorkRemarks());
			tbody.add(tr1);
		});
		jsonObject.put("analysisProjectAndResults", tab);
		//第6页数据
		JSONArray records = new JSONArray();
		tests.forEach(t->{
			JSONObject processRecord = new JSONObject();
			processRecord.put("productName", productionOrder.getProductName());
			processRecord.put("productModel", productionOrder.getProductModel());
			processRecord.put("productionBatch", productionOrder.getProductionBatch()==null?"/":productionOrder.getProductionBatch());
			ProductionTaskVO taskVO = JSON.parseObject(t.getProcessData(), ProductionTaskVO.class);
			processRecord.put("processName",taskVO.getProcessName2());
			processRecord.put("temperatureAnHumidity",taskVO.getTemperature()+"\n"+taskVO.getHumidity());
			List<ProdTaskEqInfoVO> deviceList = JSON.parseArray(t.getDeviceData(), ProdTaskEqInfoVO.class);
			List<EquipmentInventoryVO> equipmentInventoryVOS = new ArrayList<>();
			//根据设备编号查询设备信息
			deviceList.stream().forEach(x->{
				wrapper.clear();
				wrapper.eq("device_serial_number",x.getDeviceSerialNumber());
				EquipmentInventory equipment = equipmentInventoryMapper.selectOne(wrapper);
				if(null!= equipment){
					//查询设备的有效期
					String validityPeriod2 = deviceTraceabilityMapper.selectValidityPeriod(equipment.getId(),taskVO.getActualEndTime());
					EquipmentInventoryVO vo = new EquipmentInventoryVO();
					BeanUtils.copyProperties(equipment,vo);
					vo.setTracebackValidityPeriod(validityPeriod2);
					equipmentInventoryVOS.add(vo);
				}
			});
			String deviceName="";
			String deviceCode="";
			String validityDate="";
			for(EquipmentInventoryVO x : equipmentInventoryVOS){
				deviceName+=x.getDeviceName();
				deviceCode+=x.getDeviceSerialNumber();
				validityDate+=x.getTracebackValidityPeriod();
			};
			if(!StringUtils.isEmpty(deviceName)){
				processRecord.put("deviceName",deviceName+"\n");
			}else{
				processRecord.put("deviceName","/");
			}
			if(!StringUtils.isEmpty(deviceCode)){
				processRecord.put("deviceCode",deviceCode+"\n");
			}else{
				processRecord.put("deviceCode","/");
			}
			if(!StringUtils.isEmpty(validityDate)){
				processRecord.put("validityDate",validityDate+"\n");
			}else{
				processRecord.put("validityDate","/");
			}
			processRecord.put("analyticalMethod",taskVO.getJudgmentCriteria());
			processRecord.put("sampleCount",taskVO.getInspectionQuantity2());
			processRecord.put("judgmentCriteria",taskVO.getTestBasis());
			//判断工序是否有试验结果总结
			if(StringUtils.isNotBlank(taskVO.getTestResultSummary())){
				JSONObject defaultResult = new JSONObject();
				JSONObject config = new JSONObject();
				config.put("height", 300);
				config.put("margin", new int[]{5, 5, 5, 5});
				defaultResult.put("config",config);
				defaultResult.put("content",taskVO.getTestResultSummary());
				processRecord.put("defaultResult",defaultResult);
			}
			//查询工序的原始数据
			TorchResponse<List<AttachmentVO>> attachmentResponse = attachmentService.selectAttachmentListByWorkOrderAndProcess(
					workOrderNumber, taskVO.getProcessCode());
			if(StringUtils.isBlank(taskVO.getTestResultSummary()) &&
					attachmentResponse.getStatus() == Constant.REQUEST_SUCCESS && !CollectionUtils.isEmpty(attachmentResponse.getData().getData())){

				JSONObject row = new JSONObject();
				JSONObject config = new JSONObject();
				config.put("cellSpans", new int[]{3, 3});
				config.put("margin", new int[]{3, 3, 3, 3});
				config.put("rowHeights", new Integer[]{150, 15});
				row.put("config", config);
				JSONArray datas = new JSONArray();
				List<AttachmentVO> attachmentVOS = attachmentResponse.getData().getData();
				datas.add(this.createReportImgData(attachmentVOS));
				row.put("datas", datas);
				processRecord.put("result", row);
			}
			processRecord.put("reportResult","本项目分析"+taskVO.getQualifiedQuantity()+"只合格，"+taskVO.getUnqualifiedQuantity()+"只不合格");
			processRecord.put("reportWorkRemarks",taskVO.getReportWorkRemarks()==null?"/":taskVO.getReportWorkRemarks());
			processRecord.put("reprotWorkers",taskVO.getReporter4());

			if(!ObjectUtils.isEmpty(taskVO.getReportingTime0())){
				String reportDate = taskVO.getReportingTime0().toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
				processRecord.put("reprotDate",reportDate);
			}else{
				processRecord.put("reprotDate","/");
			}
			records.add(processRecord);
		});
		jsonObject.put("records", records);
		return  jsonObject;
	}

	private List<JSONObject> createReportImgData(List<AttachmentVO> attachmentVOS) {
		List<JSONObject> imgData = new ArrayList<>();
		attachmentVOS.forEach(x->{
			JSONObject data = new JSONObject();
			JSONArray dr = new JSONArray();

			JSONObject image = new JSONObject(); dr.add(image);
			image.put("dataType", "image");
			if(StringUtil.isNotBlank(x.getFilePath())) {
				image.put("content", readImage(x.getFilePath()));
				image.put("w", 200);
				image.put("h", 150);
			}
			JSONObject explan = new JSONObject(); dr.add(explan);
			explan.put("dataType", "string");
			explan.put("height", 30);
			explan.put("content", x.getDescription());
			data.put("rows", dr);
			imgData.add(data);
		});
		return imgData;
	}


	private ProductionOrderReportVO getReprotData(List<ProductionOrderVO> productionOrders) {
		List<String> workOrderNumbers = productionOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.toList());
		Collections.sort(workOrderNumbers);
		ProductionOrderReportVO reportVO = new ProductionOrderReportVO();
		reportVO.setWorkOrderNumber(workOrderNumbers.get(0));
		reportVO.setEntrustedUnit(productionOrders.get(0).getEntrustedUnit());
		reportVO.setProductModel(productionOrders.get(0).getProductModel());
		reportVO.setProductionBatch(productionOrders.get(0).getProductionBatch());
		reportVO.setQuantity( productionOrders.stream().map(ProductionOrderVO::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum));
		reportVO.setProductName(productionOrders.get(0).getProductName());
		reportVO.setManufacturer(productionOrders.get(0).getManufacturer());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		// 获取最大的完成时间（Timestamp类型）
		Optional<Timestamp> maxCompletionTime = productionOrders.stream()
				.map(ProductionOrderVO::getCompletionTime)
				.filter(Objects::nonNull)
				.max(Timestamp::compareTo);

		if (maxCompletionTime.isPresent()) {
			// 将Timestamp转换为Date，然后格式化为String
			Date date = new Date(maxCompletionTime.get().getTime());
			reportVO.setCompletionDate(sdf.format(date));
		} else {
			// 处理没有完成时间的情况
			reportVO.setCompletionDate(null); // 或者设置默认值
		}
		if(null !=productionOrders.get(0).getEntrustmentDate())
		reportVO.setEntrustmentDate(sdf.format(productionOrders.get(0).getEntrustmentDate()));
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("work_order", workOrderNumbers);
		List<ProductionOrderProcessTest> tests = productionOrderProcessTestMapper.selectList(wrapper);
		List<String> processDates = tests.stream().map(ProductionOrderProcessTest::getProcessData).collect(Collectors.toList());

		List<ProductionTaskVO> taskVOS = new ArrayList<>();

		processDates.stream().forEach(p->{
			taskVOS.add(JSON.parseObject(p, ProductionTaskVO.class));
		});

		//组装试验项目数据
		//查询失效模式的字典名称
		TorchResponse<List<Map<String,String>>> response= dicDetailService.findDicDetail("production_task_failureMode");
		Map<String, String> failureModeDictMap = new HashMap<>();
		if (response != null && response.getData() != null) {
			failureModeDictMap = response.getData().getData().stream()
					.filter(map -> map.get("value") != null && map.get("label") != null)
					.collect(Collectors.toMap(
							map -> map.get("value").toString(),
							map -> map.get("label").toString(),
							(existing, replacement) -> existing
					));
		}
		Map<String, List<ProductionTaskVO>> grouped = taskVOS.stream()
				.collect(Collectors.groupingBy(ProductionTaskVO::getProcessName2));
		final Map<String, String> finalFailureModeDictMap = failureModeDictMap;
		List<ProductionTaskVO> result = grouped.entrySet().stream()
				.map(entry -> {
					List<ProductionTaskVO> list = entry.getValue();
					ProductionTaskVO first = list.get(0);
					// 创建新的聚合对象
					ProductionTaskVO aggregated = new ProductionTaskVO();
					// 复制基准属性（以第一个记录为基准）
					aggregated.setProcessName2(first.getProcessName2());
					aggregated.setDisplayNumber(first.getDisplayNumber());
					aggregated.setTestConditions(first.getTestConditions());
					// 计算合计数量
					Integer totalQualified = list.stream()
							.map(ProductionTaskVO::getQualifiedQuantity)
							.filter(q -> q != null)
							.reduce(0, Integer::sum);
					Integer totalUnqualified = list.stream()
							.map(ProductionTaskVO::getUnqualifiedQuantity)
							.filter(q -> q != null)
							.reduce(0, Integer::sum);
					aggregated.setQualifiedQuantity(totalQualified);
					aggregated.setUnqualifiedQuantity(totalUnqualified);
					// 合并试验条件（去重）
					aggregated.setTestConditions(list.stream()
							.map(ProductionTaskVO::getTestConditions)
							.filter(condition -> condition != null && !condition.trim().isEmpty())
							.distinct()
							.collect(Collectors.joining(";")));

					// 失效模式转换和合并
					String mergedFailureMode = list.stream()
							.map(ProductionTaskVO::getFailureMode)
							.filter(mode -> mode != null && !mode.trim().isEmpty())
							.flatMap(mode -> Arrays.stream(mode.split("[；;，,]"))
									.map(String::trim)
									.filter(code -> !code.isEmpty()))
							.distinct()
							.map(code -> finalFailureModeDictMap.getOrDefault(code, code)) // 使用getOrDefault更简洁
							.collect(Collectors.joining(";"));
					aggregated.setFailureMode(mergedFailureMode.isEmpty() ? null : mergedFailureMode);
					return aggregated;
				}).collect(Collectors.toList());

		reportVO.setExperimentProjects(result);
		List<ProductionOrderResult> ordertestResults = productionOrderResultMapper.selectList(wrapper);
		List<String> resultRemarks = ordertestResults.stream().map(ProductionOrderResult::getOthereExplanationRemark).collect(Collectors.toList());
		reportVO.setRemark(resultRemarks.stream().filter(Objects::nonNull).distinct().collect(Collectors.joining(";")));
		//组装设备数据
		List<EquipmentInventoryVO> equipmentInventoryVOS = new ArrayList<>();
		for(ProductionOrderProcessTest proccessTest : tests){
			//查询工单工序任务
			ProductionTaskVO  taskVO = productionTaskService.selectProductionTaskByWorkOrderAndProcss(proccessTest.getWorkOrder(),proccessTest.getProcessCode());
			List<ProdTaskEqInfoVO> list = JSON.parseArray(proccessTest.getDeviceData(), ProdTaskEqInfoVO.class);
			//根据设备编号查询设备信息
			list.stream().forEach(x->{
				wrapper.clear();
				wrapper.eq("device_serial_number",x.getDeviceSerialNumber());
				EquipmentInventory equipment = equipmentInventoryMapper.selectOne(wrapper);
				if(null!= equipment){
					//查询设备的有效期
					String validityPeriod1 = deviceTraceabilityMapper.selectValidityPeriod(equipment.getId(),taskVO.getActualStartTime());
					String validityPeriod2 = deviceTraceabilityMapper.selectValidityPeriod(equipment.getId(),taskVO.getActualEndTime());
					EquipmentInventoryVO vo = new EquipmentInventoryVO();
					BeanUtils.copyProperties(equipment,vo);
					vo.setTracebackValidityPeriod(validityPeriod1+","+validityPeriod2);
					equipmentInventoryVOS.add(vo);
				}
			});
			reportVO.setEquipments(equipmentInventoryVOS);
		}
		//取最后一个工序任务的合个数量
		Map<String, List<ProductionTaskVO>> groupedByWorkOrder = taskVOS.stream()
				.collect(Collectors.groupingBy(ProductionTaskVO::getWorkOrderNumber));
		List<ProductionTaskVO> filteredRecords = new ArrayList<>();
		for (List<ProductionTaskVO> group : groupedByWorkOrder.values()) {
			Optional<ProductionTaskVO> maxDisplayRecord = group.stream()
					.max(Comparator.comparing(vo ->
							vo.getDisplayNumber() != null ? vo.getDisplayNumber() : Integer.MIN_VALUE));

			maxDisplayRecord.ifPresent(filteredRecords::add);
		}
		int totalQualifiedQuantity = filteredRecords.stream()
				.map(ProductionTaskVO::getQualifiedQuantity)
				.filter(Objects::nonNull)
				.mapToInt(Integer::intValue)
				.sum();
		reportVO.setQualifiedQuantity(totalQualifiedQuantity);
		int totalUnqualifiedQuantity = ordertestResults.stream()
				.filter(Objects::nonNull) // 过滤掉null对象
				.map(ProductionOrderResult::getUnqualifiedQuantity)
				.filter(Objects::nonNull) // 过滤掉null的unqualifiedQuantity
				.mapToInt(Integer::intValue)
				.sum();
		reportVO.setUnqualifiedQuantity(totalUnqualifiedQuantity);
		reportVO.setDefectiveRate(calculateUnqualifiedRate(reportVO));
		reportVO.setPreparedBy(SecurityContextHolder.getCurrentUserName());
		reportVO.setPreparedDate(sdf.format(new Date()));
		return reportVO;
	}
	public BigDecimal calculateUnqualifiedRate(ProductionOrderReportVO reportVO) {
		if (reportVO == null) {
			return BigDecimal.ZERO;
		}

		Integer quantity = reportVO.getQuantity();
		Integer unqualifiedQuantity = reportVO.getUnqualifiedQuantity();

		// 检查空值
		if (quantity == null || unqualifiedQuantity == null) {
			return BigDecimal.ZERO;
		}

		// 检查分母为零的情况
		if (quantity == 0) {
			return BigDecimal.ZERO;
		}

		// 转换为BigDecimal进行计算
		BigDecimal quantityBD = new BigDecimal(quantity);
		BigDecimal unqualifiedBD = new BigDecimal(unqualifiedQuantity);

		// 计算不合格率: (unqualified / quantity) * 100%
		BigDecimal rate = unqualifiedBD
				.divide(quantityBD, 6, RoundingMode.HALF_UP) // 中间计算保留6位小数
				.multiply(new BigDecimal("100"))
				.setScale(2, RoundingMode.HALF_UP); // 最终结果保留2位小数

		return rate;
	}

	private static byte[] readImage(String img) {
		try {
			File file = new File(img);
			FileInputStream fis = new FileInputStream(file);
			byte[] bs = new byte[(int)file.length()];
			fis.read(bs);
			fis.close();
			return bs;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("文件未找到");
		}
		return new byte[0];
	}

    @Override
    public void exportCard(HttpServletResponse response, List<String> ids) {
        exportCard(response, ids, 1); // 默认每行3张卡片
    }

    /**
     * 导出标识卡Excel（可自定义每行卡片数量）
     * @param response HTTP响应
     * @param ids 工单ID列表
     * @param cardsPerRow 每行显示的卡片数量（1=单列，2=双列，3=三列等）
     */
    public void exportCard(HttpServletResponse response, List<String> ids, int cardsPerRow) {
        try {
            // 查询标识卡数据
            List<Object> cardDataList = getIDCardDataByIds(ids);

            // 导出Excel
            IDCardExcelExporter.exportIDCards(response, cardDataList, cardsPerRow);

        } catch (Exception e) {
            log.error("标识卡导出失败", e);
            throw new ServiceException("标识卡导出失败: " + e.getMessage());
        }
    }

    /**
     * 根据工单ID列表获取标识卡数据
     *
     * @param ids 工单ID列表
     * @return 标识卡数据列表
     */
    private List<Object> getIDCardDataByIds(List<String> ids) {
        List<Object> cardDataList = new ArrayList<>();
        Map<String, String> failureModeDict = getFailureModeDictionary();
        for (String id : ids) {
            // 查询工单基础信息
            ProductionOrderVO productionOrder = awaitingProductionOrderMapper.selectProductionOrderById(id);
            if (productionOrder == null) {
                log.warn("工单不存在，ID: {}", id);
                continue;
            }

            // 查询产品信息
            ProductList productList = productListMapper.selectById(productionOrder.getProductListId());
            if (productList == null) {
                log.warn("产品信息不存在，工单ID: {}", id);
                continue;
            }

            // 查询工单检验结果
            ProductionOrderResultVO orderResult = productionOrderResultMapper.selectByWorkOrder(productionOrder.getWorkOrderNumber());

            // 判断工单类型
            boolean isDPA = DicConstant.ProductionOrder.TEST_TYPE_DPA.equals(productList.getTestType());

            if (isDPA) {
                // DPA标识卡
                ProductionOrderIDCardDPAVO dpaCard = createDPAIDCard(productionOrder, productList, orderResult);
                cardDataList.add(dpaCard);
            } else {
                // 非DPA标识卡
                ProductionOrderIDCardVO normalCard = createNormalIDCard(productionOrder, productList, orderResult);
                cardDataList.add(normalCard);

                // 如果有不合格数量，生成失效标识卡
                if (orderResult != null && orderResult.getUnqualifiedQuantity() != null && orderResult.getUnqualifiedQuantity() > 0) {
                    ProductionOrderExpiredCardVO expiredCard = createExpiredIDCard(productionOrder, productList, orderResult,failureModeDict);
                    cardDataList.add(expiredCard);
                }
            }
        }

        return cardDataList;
    }

    /**
     * 创建非DPA标识卡
     */
    private ProductionOrderIDCardVO createNormalIDCard(ProductionOrderVO productionOrder, ProductList productList, ProductionOrderResultVO orderResult) {
        ProductionOrderIDCardVO card = new ProductionOrderIDCardVO();

        // 基础信息
        card.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        card.setEntrustedUnit(productionOrder.getEntrustedUnit());
        card.setProductModel(productList.getProductModel());
        card.setProductionBatch(productList.getProductionBatch());

        // 查询最后一个工序的合格数
        Long qualifiedQuantity = productionTaskService.getLastProcessQualifiedQuantity(productionOrder.getWorkOrderNumber()).getData().getData();
        card.setQualifiedQuantity(qualifiedQuantity != null ? qualifiedQuantity.intValue() : 0);

        // 失效数量
        if (orderResult != null) {
            card.setFailureQuantity(orderResult.getUnqualifiedQuantity());
        }

        return card;
    }

    /**
     * 创建失效标识卡
     */
    private ProductionOrderExpiredCardVO createExpiredIDCard(ProductionOrderVO productionOrder,
                                                             ProductList productList, ProductionOrderResultVO orderResult,Map<String, String> failureModeDict) {
        ProductionOrderExpiredCardVO card = new ProductionOrderExpiredCardVO();

        // 基础信息
        card.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        card.setEntrustedUnit(productionOrder.getEntrustedUnit());
        card.setProductModel(productList.getProductModel());
        card.setProductionBatch(productList.getProductionBatch());
        card.setFailureQuantity(orderResult.getUnqualifiedQuantity());

        // 获取失效模式备注
        String remark = getFailureModeRemark(productionOrder.getWorkOrderNumber(),failureModeDict);
        card.setRemark(remark);

        return card;
    }

    /**
     * 创建DPA标识卡
     */
    private ProductionOrderIDCardDPAVO createDPAIDCard(ProductionOrderVO productionOrder, ProductList productList, ProductionOrderResultVO orderResult) {
        ProductionOrderIDCardDPAVO card = new ProductionOrderIDCardDPAVO();

        // 基础信息
        card.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        card.setEntrustedUnit(productionOrder.getEntrustedUnit());
        card.setEntrustedType(BusinessConstant.DPA); // 固定为DPA
        card.setProductModel(productList.getProductModel());
        card.setProductionBatch(productList.getProductionBatch());

        // 结论
        if (orderResult != null) {
            card.setConclusion(orderResult.getInspectionConclusion());
        }

        return card;
    }

    /**
     * 获取失效模式备注
     *
     * @param workOrderNumber 工单编号
     * @return 失效模式备注字符串
     */
    private String getFailureModeRemark(String workOrderNumber,Map<String, String> failureModeDict) {
        try {
            // 查询工单的所有不合格工序信息
            List<UnqualifiedProcessVO> unqualifiedProcesses = productionTaskService.getUnqualifiedTaskInfo(workOrderNumber).getData().getData();

            if (CollectionUtils.isEmpty(unqualifiedProcesses)) {
                return "";
            }


            // 收集所有失效模式
            Set<String> failureModes = new HashSet<>();
            for (UnqualifiedProcessVO process : unqualifiedProcesses) {
                if (StringUtils.isNotEmpty(process.getFailureMode())) {
                    // 从字典Map中获取对应的值
                    String dictValue = failureModeDict.get(process.getFailureMode().trim());
                    if (StringUtils.isNotEmpty(dictValue)) {
                        failureModes.add(dictValue);
                    }
                }
            }

            return String.join(",", failureModes);

        } catch (Exception e) {
            log.error("获取失效模式备注失败，工单编号: {}", workOrderNumber, e);
            return "";
        }
    }

    /**
     * 获取失效模式字典数据（一次性查询所有数据）
     *
     * @return 失效模式字典Map，key为字典编码，value为字典名称
     */
    private Map<String, String> getFailureModeDictionary() {
        try {
            TorchResponse response = dicDetailService.findDicDetail("production_task_failureMode");
            if (response != null && response.getData() != null && response.getData().getData() != null) {
                List<Map<String, String>> dictList = (List<Map<String, String>>) response.getData().getData();
                return dictList.stream()
                        .collect(Collectors.toMap(
                                item -> item.get("value"),
                                item -> item.get("label"),
                                (existing, replacement) -> existing // 如果有重复key，保留第一个
                        ));
            }
        } catch (Exception e) {
            log.error("查询失效模式字典失败", e);
        }
        return new HashMap<>();
    }

	public static void main(String[] args) {
		//☑
		//□
		/**
		 * 1 、 不能进行测试的原因
		 * □   无相应器件详细资料
		 * □   现有测试设备硬件资源不够（硬件资源）
		 * ☑   无相应器件测试应用软件（硬件资源）
		 * □   无相应测试适配器（特殊封装无测试夹具）
		 * □   测试设备有故障
		 * □   规范无此分类
		 * 2 、 不能进行功率老化原因
		 * □  无相应器件详细资料
		 * □  无相应老化设备
		 * □  有设备但无相应老化板
		 * □  老化设备有故障
		 */
		ReportDocxTool rt = new ReportDocxTool();
		JSONObject jsonData = new JSONObject();
		jsonData.put("p0", "北京航宇创通技术股份有限公司");
		jsonData.put("p1", "存储器");
		jsonData.put("p2", "S29GL01GP13TFIV10");
		jsonData.put("p3", "2010                                     ");
		jsonData.put("p4", createArrayP5());
		jsonData.put("p7", createArrayP7());
		String template = BusinessConstant.TEMPLATEPATH+"生产部-不可筛报告.docx";
		rt.writeDOCX(template, jsonData,  BusinessConstant.TEMPLATEPATH+"table_" + System.currentTimeMillis() + ".docx");
	}

	private static Object createArrayP5() {

		JSONArray array = new JSONArray();
		JSONObject object1 = new JSONObject();
		object1.put("p5", "□");
		object1.put("p6", "现有测试设备硬件资源不够（硬件资源）");
		array.add(object1);
		
		JSONObject object2 = new JSONObject();
		object2.put("p5", "☑");
		object2.put("p6", "无相应器件详细资料");
		array.add(object2);
		return array;
	}
	private static Object createArrayP7() {

		JSONArray array = new JSONArray();
		JSONObject object1 = new JSONObject();
		object1.put("p8", "□");
		object1.put("p9", "现有测试设备硬件资源不够（硬件资源）");
		array.add(object1);

		JSONObject object2 = new JSONObject();
		object2.put("p8", "☑");
		object2.put("p9", "无相应器件详细资料");
		array.add(object2);
		return array;
	}

}