package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetImportVO;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;

import java.util.List;

import java.util.Map;

/**
* @description 能力资产Service
* <AUTHOR>
* @date 2025-08-04
**/
public interface CapabilityAssetService {
    
    /**
	 * 分页查找查找 能力资产
	 * 
	 * @param dto 能力资产dto实体对象
	 * @return 
	 */
	TorchResponse<List<CapabilityAssetVO>> findCapabilityAssetPage(CapabilityAssetDTO dto);

    /**
	 * 添加 \修改 能力资产
	 * 
	 * @param capabilityAssetDto 能力资产dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(CapabilityAssetDTO capabilityAssetDto);
	
	/**
	 * 通过id查找能力资产
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<CapabilityAssetVO> findCapabilityAsset(String id);
	
	/**
	 * 删除 能力资产
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 能力资产
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<CapabilityAssetVO>> getOptionsList(String id);




    /**
     * 联动数据查询
     *
     * @param linkageDataTableName
     * @param conditionalValue
     * @return
     */
    TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue);

    Map<String,String> selectDataLinkageByProductModel(String product_model);
    Map<String,String> selectDataLinkageByTaskNumber(String task_number);
    /**
     * 根据条件查询能力资产列表
     *
     * @param dto 能力资产信息
     * @return 能力资产集合信息
     */
    List<CapabilityAssetVO> selectCapabilityAssetList(CapabilityAssetDTO dto);

    /**
     * 导入能力资产数据
     *
     * @param capabilityAssetList 能力资产数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importCapabilityAsset(List<CapabilityAssetImportVO> capabilityAssetList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取能力资产数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectCapabilityAssetListByIds(List<String> ids);
	
	/**
	 * 根据多个产品型号和能力类型组合查询能力资产列表，并设置核验结果
	 * @param queryParams 查询参数列表，包含产品型号和能力类型组合
	 * @return 包含核验结果的参数列表
	 */
	TorchResponse<List<CapabilityVerificationCheckDTO>> selectCapabilityAssetListByMultiParams(List<CapabilityVerificationCheckDTO> queryParams);

}