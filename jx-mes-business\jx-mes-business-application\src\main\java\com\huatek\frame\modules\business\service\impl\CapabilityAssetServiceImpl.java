package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.HashMap;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.DeviceType;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetImportVO;
import com.huatek.frame.modules.business.mapper.DeviceTypeMapper;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.CapabilityAsset;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.mapper.CapabilityAssetMapper;
import com.huatek.frame.modules.business.service.CapabilityAssetService;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import java.sql.Date;
import com.huatek.frame.modules.business.domain.ProductManagement;
import com.huatek.frame.modules.business.mapper.ProductManagementMapper;
import com.huatek.frame.modules.business.domain.CapabilityDevelopment;
import com.huatek.frame.modules.business.mapper.CapabilityDevelopmentMapper;
import org.springframework.util.CollectionUtils;



/**
 * 能力资产 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "capabilityAsset")
//@RefreshScope
@Slf4j
public class CapabilityAssetServiceImpl implements CapabilityAssetService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private CapabilityAssetMapper capabilityAssetMapper;

	@Autowired
    private ProductManagementMapper productManagementMapper;
	@Autowired
    private CapabilityDevelopmentMapper capabilityDevelopmentMapper;
    @Autowired
    private CodeManagementService codeManagementService;
    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public CapabilityAssetServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<CapabilityAssetVO>> findCapabilityAssetPage(CapabilityAssetDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<CapabilityAssetVO> capabilityAssets = capabilityAssetMapper.selectCapabilityAssetPage(dto);
		TorchResponse<List<CapabilityAssetVO>> response = new TorchResponse<List<CapabilityAssetVO>>();
		response.getData().setData(capabilityAssets);
		response.setStatus(200);
		response.getData().setCount(capabilityAssets.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(CapabilityAssetDTO capabilityAssetDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(capabilityAssetDto.getCodexTorchDeleted())) {
            capabilityAssetDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = capabilityAssetDto.getId();
		CapabilityAsset entity = new CapabilityAsset();
        BeanUtils.copyProperties(capabilityAssetDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			capabilityAssetMapper.insert(entity);
		} else {
			capabilityAssetMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        CapabilityAssetVO vo = new CapabilityAssetVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<CapabilityAssetVO> findCapabilityAsset(String id) {
		CapabilityAssetVO vo = new CapabilityAssetVO();
		if (!HuatekTools.isEmpty(id)) {
			CapabilityAsset entity = capabilityAssetMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<CapabilityAssetVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<CapabilityAsset> capabilityAssetList = capabilityAssetMapper.selectBatchIds(Arrays.asList(ids));
        for (CapabilityAsset capabilityAsset : capabilityAssetList) {
            capabilityAsset.setCodexTorchDeleted(Constant.DEFAULT_YES);
            capabilityAssetMapper.updateById(capabilityAsset);
        }
		//capabilityAssetMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("productModel",capabilityAssetMapper::selectOptionsByProductModel);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("taskNumber",capabilityAssetMapper::selectOptionsByTaskNumber);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}




    @Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "product_management":
                    data = selectDataLinkageByProductModel(conditionalValue);
                    break;
                case "capability_development":
                    data = selectDataLinkageByTaskNumber(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByProductModel(String product_model) {
        return capabilityAssetMapper.selectDataLinkageByProductModel(product_model);
    }
    @Override
    public Map<String,String> selectDataLinkageByTaskNumber(String task_number) {
        return capabilityAssetMapper.selectDataLinkageByTaskNumber(task_number);
    }

    @Override
    @ExcelExportConversion(tableName = "capability_asset", convertorFields = "capabilityType,capability_asset_applicableTestType#applicableTestType")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CapabilityAssetVO> selectCapabilityAssetList(CapabilityAssetDTO dto) {
        return capabilityAssetMapper.selectCapabilityAssetList(dto);
    }

    /**
     * 导入能力资产数据
     *
     * @param capabilityAssetList 能力资产数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "capability_asset", convertorFields = "capabilityType,capability_asset_applicableTestType#applicableTestType")
    public TorchResponse importCapabilityAsset(List<CapabilityAssetImportVO> capabilityAssetList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(capabilityAssetList) || capabilityAssetList.size() == 0) {
            throw new ServiceException("导入能力资产数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CapabilityAssetImportVO vo : capabilityAssetList) {
            String message="产品型号=" + vo.getProductModel() + "产品名称=" + vo.getProductName() + "生产厂家=" + vo.getManufacturer();
            try {
                CapabilityAsset capabilityAsset = new CapabilityAsset();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, capabilityAsset);
                capabilityAsset.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
                capabilityAsset.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
                capabilityAsset.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
//                QueryWrapper<CapabilityAsset> wrapper = new QueryWrapper();
//                CapabilityAsset oldCapabilityAsset = null;
//                // 验证是否存在这条数据
//                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
//                    for (String unionColumn: unionColumns) {
//                        try {
//                            Field field = CapabilityAssetVO.class.getDeclaredField(unionColumn);
//                            field.setAccessible(true);
//                            Object value = field.get(vo);
//                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
//                            wrapper.eq(dbColumnName, value);
//                        } catch (Exception e) {
//                            throw new ServiceException("导入数据失败");
//                        }
//                    }
//                    List<CapabilityAsset> oldCapabilityAssetList = capabilityAssetMapper.selectList(wrapper);
//                    if (!CollectionUtils.isEmpty(oldCapabilityAssetList) && oldCapabilityAssetList.size() > 1) {
//                        capabilityAssetMapper.delete(wrapper);
//                    } else if (!CollectionUtils.isEmpty(oldCapabilityAssetList) && oldCapabilityAssetList.size() == 1) {
//                        oldCapabilityAsset = oldCapabilityAssetList.get(0);
//                    }
//                }
//                if (StringUtils.isNull(oldCapabilityAsset)) {
                    BeanValidators.validateWithException(validator, vo);
                    //获取能力编号
                    TorchResponse responseCode = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_VERIFICATION_NLZC);
                    capabilityAsset.setCapabilityNumber(responseCode.getData().getData().toString());
                    capabilityAssetMapper.insert(capabilityAsset);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、" + message + " 导入成功");
//                } else if (isUpdateSupport) {
//                    BeanValidators.validateWithException(validator, vo);
//                    BeanUtil.copyProperties(vo, oldCapabilityAsset, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
//                    capabilityAssetMapper.updateById(oldCapabilityAsset);
//                    successNum++;
//                    successMsg.append("<br/>" + successNum + "、 " + msg + " 更新成功");
//                }  else {
//                    failureNum++;
//                    failureMsg.append("<br/>" + failureNum + "、 " + msg + " 已存在");
//                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、 " + message + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CapabilityAssetImportVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();

        //判断不能为空
//        if (HuatekTools.isEmpty(vo.getCapabilityNumber())) {
//            failureRecord++;
//            failureNotNullMsg.append("<br/>" + failureRecord + "=>能力编号不能为空!");
//        }

        if (HuatekTools.isEmpty(vo.getCapabilityType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>能力类型不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getApplicableEquipmentType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>适用设备类型不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getProductModel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=产品型号不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getProductName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>产品名称不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getManufacturer())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产厂家不能为空!");
        }

        //判断设备类型编码是否存在设备类型表中
        if (!HuatekTools.isEmpty(vo.getApplicableEquipmentType()) ) {
                DeviceType deviceType = deviceTypeMapper.selectOne(
                        new QueryWrapper<DeviceType>()
                                .eq("device_type_code", vo.getApplicableEquipmentType())
                );
            if (null==deviceType) {
                failureRecord++;
                failureRecordMsg.append("适用设备类型=" + vo.getApplicableEquipmentType() + "; ");
            }
        }

        if (!HuatekTools.isEmpty(vo.getProductModel()) &&!HuatekTools.isEmpty(vo.getProductName()) && !HuatekTools.isEmpty(vo.getManufacturer()) ) {

            ProductManagement productManagement = productManagementMapper.selectOne(
                    new QueryWrapper<ProductManagement>()
                            .eq("product_model", vo.getProductModel())
                            .eq("product_name", vo.getProductName())
                            .eq("manufacturer", vo.getManufacturer())
            );
            if (null==productManagement) {
                failureRecord++;
                failureRecordMsg.append("产品型号=" + vo.getProductModel() + "产品名称=" + vo.getProductName() + "生产厂家=" + vo.getManufacturer() + "; ");
            }else{
                vo.setProductCategory(productManagement.getProductCategory());
            }
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectCapabilityAssetListByIds(List<String> ids) {
        List<CapabilityAssetVO> capabilityAssetList = capabilityAssetMapper.selectCapabilityAssetListByIds(ids);

		TorchResponse<List<CapabilityAssetVO>> response = new TorchResponse<List<CapabilityAssetVO>>();
		response.getData().setData(capabilityAssetList);
		response.setStatus(200);
		response.getData().setCount((long)capabilityAssetList.size());
		return response;
    }
    
    @Override
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<CapabilityVerificationCheckDTO>> selectCapabilityAssetListByMultiParams(List<CapabilityVerificationCheckDTO> queryParams) {
        List<CapabilityAssetVO> capabilityAssetList = capabilityAssetMapper.selectCapabilityAssetListByMultiParams(queryParams);

        // 创建一个Map用于存储查询结果，键为verificationType+productModel的组合
        Map<String, Boolean> resultMap = new HashMap<>();
        
        // 将查询结果放入Map中
        for (CapabilityAssetVO asset : capabilityAssetList) {
            String key = asset.getCapabilityType() + "_" + asset.getProductModel();
            resultMap.put(key, true);
        }
        
        // 根据查询结果设置verificationResult
        for (CapabilityVerificationCheckDTO param : queryParams) {
            String key = param.getVerificationType() + "_" + param.getProductModel();
            // 如果查询到数据，设置为"1"（不具备），否则设置为"0"（具备）
            if (resultMap.containsKey(key)) {
                param.setVerificationResult(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_VERIFICATIONRESULT_YES);
            } else {
                param.setVerificationResult(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_VERIFICATIONRESULT_NO);
            }
        }

		TorchResponse<List<CapabilityVerificationCheckDTO>> response = new TorchResponse<List<CapabilityVerificationCheckDTO>>();
		response.getData().setData(queryParams);
		response.setStatus(200);
		response.getData().setCount((long)queryParams.size());
		return response;
    }


}
