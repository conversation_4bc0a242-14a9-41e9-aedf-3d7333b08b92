<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProdTaskOpHistMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.task_number as taskNumber,
		t.reason as reason,
		t.completed_quantity as completedQuantity,
		t.`comment` as `comment`,
		t.operation_type as operationType,
        t.previous_status as previousStatus,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectProdTaskOpHistPage" parameterType="com.huatek.frame.modules.business.service.dto.ProdTaskOpHistDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdTaskOpHistVO">
		select
		<include refid="Base_Column_List" />
			from prod_task_op_hist t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="taskNumber != null and taskNumber != ''">
                    and t.task_number  like concat('%', #{taskNumber} ,'%')
                </if>
                <if test="reason != null and reason != ''">
                    and t.reason  = #{reason}
                </if>
                <if test="completedQuantity != null and completedQuantity != ''">
                    and t.completed_quantity  = #{completedQuantity}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="operationType != null and operationType != ''">
                    and t.operation_type  = #{operationType}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProdTaskOpHistList" parameterType="com.huatek.frame.modules.business.service.dto.ProdTaskOpHistDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdTaskOpHistVO">
		select
		<include refid="Base_Column_List" />
			from prod_task_op_hist t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="taskNumber != null and taskNumber != ''">
                    and t.task_number  like concat('%', #{taskNumber} ,'%')
                </if>
                <if test="reason != null and reason != ''">
                    and t.reason  = #{reason}
                </if>
                <if test="completedQuantity != null and completedQuantity != ''">
                    and t.completed_quantity  = #{completedQuantity}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="operationType != null and operationType != ''">
                    and t.operation_type  = #{operationType}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProdTaskOpHistListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdTaskOpHistVO">
		select
		<include refid="Base_Column_List" />
			from prod_task_op_hist t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>