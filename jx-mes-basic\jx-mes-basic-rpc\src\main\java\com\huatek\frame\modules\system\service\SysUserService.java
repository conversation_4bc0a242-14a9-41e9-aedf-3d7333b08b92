package com.huatek.frame.modules.system.service;

import java.util.List;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.service.dto.DataRuleDTO;
import com.huatek.frame.modules.system.service.dto.RoleDTO;
import com.huatek.frame.modules.system.service.dto.SysUserDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 系统_用户 Service
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:46
 */
public interface SysUserService {

	/**
	 * 分页查找查找 系统_用户
	 * 
	 * @param dto 系统_用户dto
	 * @return map map
	 */
	TorchResponse<List<SysUser>> findUserPage(SysUserDTO dto);

	/**
	 * 添加 \修改系统_用户
	 * 
	 * @param sysUser
	 *            系统_用户user
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(SysUser sysUser);

	/**
	 * 通过id查找 系统_用户
	 *
	 * @param id
	 *            主键
	 * @return 
	 */
	TorchResponse<SysUserVO> findUser(String id);

	/**
	 * 删除 系统_用户
	 * 
	 * @param ids 主键集合  
	 * @return map map
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 更新用户角色关系
	 * 
	 * @param uid
	 * @param dtos
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse updateUserRole(String uid, List<DataRuleDTO> dtos);

	/**
	 * 第三方授权登录用户信息初始化逻辑
	 *
	 *  @param sysUser
	 * @return
     */
    SysUser initOauthChannelUserInfo(SysUser sysUser);

    TorchResponse test();

	/**
	 * 查询用户资料信息
	 * @param token
	 * @return
	 */
	TorchResponse getProfile(String token);

	/**
	 * 更新用户资料信息
	 *
	 * @param sysUser
	 * @return
	 */
	TorchResponse updateProfile(SysUser sysUser);

	/**
	 * 更新用户头像
	 *
	 * @param file
	 * @param token
	 * @return
	 */
	TorchResponse avatar(MultipartFile file, String token);

	/**
	 * 用户Id
	 * @param userId
	 * @return
	 */
    TorchResponse resetPwd(String userId);

	/**
	 * 根据角色查询用户列表
	 *
	 * @param dto
	 * @return
	 */
	List<SysUser> findUserList(RoleDTO dto);

	/**
	 * 查询系统用户列表
	 * @param dto
	 * @return
	 */
	List<SysUserVO> selectUserList(SysUserDTO dto);

	/**
	 * 导入系统用户
	 * @param list
	 * @param unionColumns
	 * @param isUpdateSupport
	 * @param operName
	 * @return
	 */
	TorchResponse importUser(List<SysUserVO> list, List<String> unionColumns, boolean isUpdateSupport, String operName);

	/**
	 * 从用户上下文获取当前用户所属部门
	 * @return
	 */
    String getCurrentUserGroupId();

	/**
	 * 查询所有用户
	 * @return
	 */
    TorchResponse<List<SysUserVO>> findAllUsers();

	SysUser selectById(String id);

	/**
	 * 根据部门获取该部门下所有用户
	 * @param requestParam 部门ID
	 * @return
	 */
    List<SysUserVO> getUsersByDepart(String requestParam);

	/**
	 * 根据用户名称获取对应id
	 * @param names
	 * @return
	 */
	List<SysUserVO> getUsersByNames(String[] names);

	/**
	 * 根据用户id获取对应部门
	 * @param userId
	 * @return
	 */
	TorchResponse<SysGroupVO> findDepartById(String userId);
	/**
	 * 根据角色编码获取用户id
	 * @param roleCode
	 * @return
	 */
	List<String> selectUserIdsByRole(String roleCode);

	/**
	 * 根据角色id查询所有人员
	 */
	List<String> selectUsersByRoleId(String roleId);

	List<String> selectCurrentUserRoles(String currentUserId);
}
