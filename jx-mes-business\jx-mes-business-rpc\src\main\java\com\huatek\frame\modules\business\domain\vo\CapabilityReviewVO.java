package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 能力评审VO实体类
* <AUTHOR>
* @date 2025-08-06
**/
@Data
@ApiModel("能力评审DTO实体类")
public class CapabilityReviewVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("产品资料编号")
    private String productInformation1Number;

    /**
	 * 检验类型
     **/
    @ApiModelProperty("检验类型")
    @Excel(name = "检验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String inspectionType;

    /**
	 * 类型
     **/
    @ApiModelProperty("类型")
    @Excel(name = "类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String type;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;

    /**
	 * 标准/规范编号
     **/
    @ApiModelProperty("标准/规范编号")
    @Excel(name = "标准/规范编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String standardSpecificationNumber0;

    /**
	 * 评审结果
     **/
    @ApiModelProperty("评审结果")
    @Excel(name = "评审结果",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reviewResult;

    /**
	 * 反馈/处理
     **/
    @ApiModelProperty("反馈/处理")
    @Excel(name = "反馈/处理",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String feedbackProcessing;

    //是否自动评审
    @ApiModelProperty("是否自动评审")
    private String isAuto;
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;

    /**
	 * 反馈/评审人
     **/
    @ApiModelProperty("反馈/评审人")
    @Excel(name = "反馈/评审人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reviewerOfFeedback;

    /**
	 * 反馈/评审时间
     **/
    @ApiModelProperty("反馈/评审时间")
    @Excel(name = "反馈/评审时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp reviewTimeOfFeedback;

    /**
	 * 生产工单
     **/
    @ApiModelProperty("生产工单")
    @Excel(name = "生产工单",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productionWorkOrder;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
	 * 提交人
     **/
    @ApiModelProperty("提交人")
    @Excel(name = "提交人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String submitter;

    /**
	 * 提交时间
     **/
    @ApiModelProperty("提交时间")
    @Excel(name = "提交时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp submissionTime;

    /**
	 * 关联异常反馈单号
     **/
    @ApiModelProperty("关联异常反馈单号")
    @Excel(name = "关联异常反馈单号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String assocExceptionFeedbackNum;


    /**
     * 能力核验id
     **/
    @ApiModelProperty("能力核验id")
    @Excel(name = "能力核验id",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String capabilityVerificationId;
    /**
     * 产品列表id
     **/
    @ApiModelProperty("产品列表id")
    @Excel(name = "产品列表id",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productListId;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}