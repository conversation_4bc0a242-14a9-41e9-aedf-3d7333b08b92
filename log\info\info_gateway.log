2025-09-09 09:46:17,207 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 09:46:17,207 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 09:46:17,222 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 09:46:17,744 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 09:53:21,788 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 09:53:21,795 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 09:53:21,851 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 09:53:21,970 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 09:53:25,835 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductInformationManagement
2025-09-09 09:53:25,837 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 09:53:25,839 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInformationManagement/productInformationManagementList
2025-09-09 10:26:08,649 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInformationManagement/productInformationManagementList
2025-09-09 10:26:10,617 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInformationManagement/productInformationManagementList
2025-09-09 10:26:11,872 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInformationManagement/productInformationManagementList
2025-09-09 10:26:25,884 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 10:26:25,900 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 10:26:25,926 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 10:26:26,159 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 10:26:28,100 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 10:26:28,101 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInformationManagement/productInformationManagementList
2025-09-09 10:26:28,119 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductInformationManagement
2025-09-09 10:28:48,377 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-09 10:28:48,387 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/evaluationOrderList
2025-09-09 10:28:48,515 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-09 10:28:48,669 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:28:48,982 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:28:49,055 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-09 10:28:49,121 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-09 10:28:49,172 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 10:28:49,235 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 10:28:52,672 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 10:28:52,674 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductList
2025-09-09 10:28:52,674 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-09 10:28:52,685 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/detail/1965239442862157826
2025-09-09 10:28:52,841 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/optionsList/standardSpecificationNumber
2025-09-09 10:28:52,841 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-09 10:28:52,940 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 10:28:53,121 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:28:53,141 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-09 10:28:53,276 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:28:53,275 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 10:28:53,367 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_specialAnalysisTestProject
2025-09-09 10:28:53,450 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/otherContacts/otherContactsList/1948218882877730817
2025-09-09 10:28:53,515 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_groupType
2025-09-09 10:28:53,583 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_inspectionTestProject
2025-09-09 10:28:53,765 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/customerManager
2025-09-09 10:28:53,816 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_qualityConsistencyTestItems
2025-09-09 10:28:53,875 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-09 10:28:53,911 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_dpaTestProject
2025-09-09 10:28:54,049 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-09 10:28:54,097 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_status
2025-09-09 10:28:54,184 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-09 10:28:54,370 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataFormat
2025-09-09 10:28:54,471 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqERep
2025-09-09 10:28:54,535 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqsPapereport
2025-09-09 10:28:54,605 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-09 10:28:54,705 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-09 10:28:54,781 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 10:29:01,446 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/evaluationOrderList
2025-09-09 10:29:01,464 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-09 10:29:01,532 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-09 10:29:01,612 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:29:01,867 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:29:01,995 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-09 10:29:02,028 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-09 10:29:02,068 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 10:29:02,116 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 10:29:04,165 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 10:29:04,165 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductList
2025-09-09 10:29:04,165 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-09 10:29:04,178 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/detail/1965239442862157826
2025-09-09 10:29:04,290 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/optionsList/standardSpecificationNumber
2025-09-09 10:29:04,308 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-09 10:29:04,355 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 10:29:04,417 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-09 10:29:04,476 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:29:04,584 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 10:29:04,586 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 10:29:04,725 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_specialAnalysisTestProject
2025-09-09 10:29:04,801 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/otherContacts/otherContactsList/1948218882877730817
2025-09-09 10:29:04,848 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_groupType
2025-09-09 10:29:04,988 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/customerManager
2025-09-09 10:29:05,036 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_inspectionTestProject
2025-09-09 10:29:05,267 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-09 10:29:05,326 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_qualityConsistencyTestItems
2025-09-09 10:29:05,506 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-09 10:29:05,608 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_dpaTestProject
2025-09-09 10:29:05,778 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-09 10:29:06,588 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_status
2025-09-09 10:29:07,144 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataFormat
2025-09-09 10:29:07,290 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqERep
2025-09-09 10:29:07,417 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqsPapereport
2025-09-09 10:29:07,492 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-09 10:29:07,588 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-09 10:29:07,659 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 10:29:09,251 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-09 10:29:09,252 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 10:29:16,959 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 10:29:16,966 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 10:29:16,973 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 10:29:17,046 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 10:29:18,953 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 10:29:36,580 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardSpecification/standardSpecificationList
2025-09-09 10:29:36,591 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardSpecification/optionsList/entrustedUnit
2025-09-09 10:29:36,595 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_status
2025-09-09 10:29:36,634 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardSpecification
2025-09-09 10:29:36,591 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_specificationType
2025-09-09 10:29:37,197 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_specificationType
2025-09-09 10:29:37,278 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_status
2025-09-09 10:29:42,088 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/add
2025-09-09 10:29:47,923 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/add
2025-09-09 10:29:48,327 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 10:31:45,549 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 10:31:45,550 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 10:31:45,552 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 10:31:45,574 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 10:31:45,576 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 10:31:45,596 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:31:45,605 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 10:31:45,650 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 10:31:45,656 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 10:31:45,667 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 10:31:45,793 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 10:31:45,857 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 10:31:45,956 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 10:31:46,129 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 10:31:46,236 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 10:31:51,507 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 10:31:51,583 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:31:52,072 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 10:31:52,148 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 10:31:52,188 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 10:31:52,562 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 10:31:52,622 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 10:31:58,786 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/batchUpdate
2025-09-09 10:31:59,650 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 10:32:10,370 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:32:13,184 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:32:14,267 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:32:17,286 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:32:18,363 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:32:20,806 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:32:29,593 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/batchUpdate
2025-09-09 10:36:05,120 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 10:36:05,186 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 10:36:05,243 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 10:36:06,746 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 10:36:06,797 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 10:36:06,834 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 10:37:49,199 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:39:28,891 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 10:39:34,709 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 10:39:34,710 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 10:39:34,717 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 10:39:34,798 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 10:39:48,220 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_status
2025-09-09 10:39:48,223 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_specificationType
2025-09-09 10:39:48,226 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardSpecification/optionsList/entrustedUnit
2025-09-09 10:39:48,231 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardSpecification/standardSpecificationList
2025-09-09 10:39:48,244 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardSpecification
2025-09-09 10:39:48,331 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_specificationType
2025-09-09 10:39:48,389 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_status
2025-09-09 10:39:53,353 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/add
2025-09-09 10:39:54,001 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 10:39:59,602 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/batchUpdate
2025-09-09 10:43:08,776 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:43:09,804 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:43:12,280 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 10:43:12,288 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:43:12,324 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 10:43:12,373 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 10:43:12,506 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 10:43:12,660 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 10:43:12,699 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 10:43:13,670 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:43:13,683 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 10:43:15,662 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 10:43:16,284 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 10:43:16,363 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 10:43:16,425 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 10:43:16,468 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 10:43:16,648 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:44:50,987 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:44:54,239 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 10:54:10,224 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/cardinventory_dutBoardType
2025-09-09 10:54:10,245 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/cardinventory/cardinventoryList
2025-09-09 10:54:10,246 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Cardinventory
2025-09-09 10:54:10,601 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/cardinventory_dutBoardType
2025-09-09 11:13:10,602 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/cardinventory/export
2025-09-09 11:15:10,121 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 11:15:10,122 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 11:15:10,122 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 11:15:10,137 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 11:15:10,141 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 11:15:10,142 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 11:15:10,219 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 11:15:10,224 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 11:15:10,300 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 11:15:10,342 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 11:15:10,444 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 11:15:10,513 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 11:15:10,665 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:15:10,869 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 11:15:10,924 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 11:17:31,428 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/export
2025-09-09 11:17:54,306 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 11:17:54,313 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 11:17:54,344 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 11:17:54,374 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 11:17:54,415 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:17:54,549 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 11:17:54,915 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 11:17:58,485 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 11:17:58,494 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 11:17:58,537 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 11:17:58,685 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 11:18:00,497 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 11:18:00,502 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 11:18:00,516 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 11:18:00,516 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 11:18:00,516 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 11:18:00,529 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 11:18:00,564 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 11:18:00,582 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 11:18:00,615 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 11:18:00,721 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 11:18:00,733 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 11:18:01,100 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 11:18:01,192 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 11:18:01,244 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:18:01,299 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 11:18:01,336 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 11:18:07,931 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 11:18:07,937 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 11:18:07,973 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 11:18:08,006 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 11:18:08,274 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:18:08,340 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 11:18:08,369 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 11:18:15,076 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/export
2025-09-09 11:22:06,139 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 11:22:06,147 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 11:22:06,238 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 11:22:06,259 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 11:22:06,653 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 11:22:06,804 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 11:22:06,856 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 11:22:06,892 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 11:22:06,905 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 11:22:07,430 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 11:22:07,482 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 11:22:07,637 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 11:22:14,132 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 11:22:14,137 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 11:22:14,143 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 11:22:14,167 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 11:22:14,168 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 11:22:14,172 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 11:22:14,197 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 11:22:14,225 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 11:22:14,234 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 11:22:14,254 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 11:22:14,262 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 11:22:14,442 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 11:22:14,452 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 11:22:14,458 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 11:22:14,479 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 11:22:14,554 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 11:22:14,559 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 11:22:14,761 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 11:22:14,772 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 11:22:14,777 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 11:22:14,784 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 11:22:14,785 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 11:22:14,785 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 11:22:15,019 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 11:22:15,074 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 11:22:15,119 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:22:15,218 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 11:22:15,246 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 11:22:15,327 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:22:15,338 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 11:22:15,402 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 11:22:15,465 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 11:22:16,224 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 11:22:16,240 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductList
2025-09-09 11:22:16,240 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-09 11:22:16,242 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 11:22:16,314 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/detail/1965239442862157826
2025-09-09 11:22:16,477 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-09 11:22:16,484 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/optionsList/standardSpecificationNumber
2025-09-09 11:22:16,567 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-09 11:22:16,742 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 11:22:16,772 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-09 11:22:16,805 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-09 11:22:16,836 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 11:22:17,033 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/otherContacts/otherContactsList/1948218882877730817
2025-09-09 11:22:17,215 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_specialAnalysisTestProject
2025-09-09 11:22:17,465 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerInformationManagement/getMarketUsers
2025-09-09 11:22:17,504 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_groupType
2025-09-09 11:22:17,712 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_inspectionTestProject
2025-09-09 11:22:17,763 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-09 11:22:17,779 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_qualityConsistencyTestItems
2025-09-09 11:22:17,860 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-09 11:22:17,879 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_dpaTestProject
2025-09-09 11:22:17,934 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-09 11:22:17,953 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_status
2025-09-09 11:22:18,016 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataFormat
2025-09-09 11:22:18,178 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqERep
2025-09-09 11:22:18,259 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqsPapereport
2025-09-09 11:22:18,362 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-09 11:22:18,531 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-09 11:22:18,595 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 11:24:45,162 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 11:24:45,165 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 11:24:45,168 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:24:45,172 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 11:24:45,174 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 11:24:45,183 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 11:24:45,329 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 11:24:45,494 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 11:24:45,678 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:24:45,801 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 11:24:45,844 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 11:30:03,619 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 11:30:03,629 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 11:30:03,678 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 11:30:03,903 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 11:30:04,771 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 11:30:04,876 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 11:30:08,056 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 11:30:08,065 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:30:08,065 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 11:30:08,066 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 11:30:08,066 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 11:30:08,078 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 11:30:08,119 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 11:30:08,249 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 11:30:08,410 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 11:30:08,543 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 11:30:08,723 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 11:30:08,798 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 13:16:02,892 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/business/api/capabilityAsset/importTemplate
2025-09-09 13:16:03,366 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 58] token is empty...
2025-09-09 13:16:21,302 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 13:16:24,266 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 13:17:14,374 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/business/api/capabilityAsset/importTemplate
2025-09-09 13:24:27,969 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/business/api/capabilityAsset/importTemplate
2025-09-09 13:24:42,262 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/business/api/capabilityAsset/importTemplate
2025-09-09 13:25:33,227 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/business/api/capabilityAsset/importTemplate
2025-09-09 13:25:40,614 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-09 13:48:35,206 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 13:48:35,206 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 13:48:35,246 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 13:48:36,240 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 14:11:29,593 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:11:29,594 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 14:11:29,651 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 14:11:29,807 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:11:29,986 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:12:35,754 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:12:43,526 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 14:12:43,535 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 14:12:43,574 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 14:12:43,837 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 14:12:45,638 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 14:12:45,649 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:12:45,650 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:12:45,655 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 14:12:45,659 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:12:45,665 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:12:45,720 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 14:12:46,100 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:12:46,444 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:12:46,583 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:12:46,770 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:12:46,844 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 14:13:16,186 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/business/api/capabilityAsset/importData
2025-09-09 14:14:00,372 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-09 14:14:12,604 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importData
2025-09-09 14:33:59,544 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 14:33:59,544 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-09 14:33:59,544 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-09 14:33:59,555 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-09 14:33:59,702 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-09 14:33:59,945 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:34:00,097 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-09 14:34:00,318 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-09 14:34:00,446 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:34:00,491 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-09 14:34:00,536 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-09 14:38:34,066 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-09 14:38:39,867 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-09 14:39:35,514 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-09 14:45:39,222 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/deviceType/cascade/null
2025-09-09 14:45:39,222 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/DeviceType
2025-09-09 14:48:42,194 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 14:48:42,384 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:48:42,405 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 14:48:42,763 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:48:42,890 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:50:43,565 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:50:51,904 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 14:50:51,915 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 14:50:51,964 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 14:50:52,236 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 14:50:54,382 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 14:50:54,392 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 14:50:54,392 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:50:54,393 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:50:54,393 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:50:54,414 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:50:54,604 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 14:50:54,931 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:50:55,219 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:50:55,291 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:50:55,370 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:50:55,434 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 14:50:57,772 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-09 14:51:52,490 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 14:51:52,490 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 14:51:52,499 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 14:51:52,748 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 14:53:14,078 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/deviceType/cascade/null
2025-09-09 14:53:14,078 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/DeviceType
2025-09-09 14:53:48,497 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importData
2025-09-09 14:54:05,128 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 14:54:05,140 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 14:54:05,158 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 14:54:05,345 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 14:54:09,518 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/deviceType/cascade/null
2025-09-09 14:54:09,519 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/DeviceType
2025-09-09 14:54:58,181 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importData
2025-09-09 14:56:28,427 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 14:56:28,427 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-09 14:56:28,431 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-09 14:56:28,571 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-09 14:57:14,595 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 14:57:14,597 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:57:14,604 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:57:14,947 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 14:57:14,947 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:57:15,712 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:57:15,803 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:57:15,880 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:57:15,939 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:57:16,004 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:57:16,083 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 14:57:39,164 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importData
2025-09-09 14:57:49,780 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:58:17,057 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherIncludesTime
2025-09-09 14:58:17,057 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CodeManagement
2025-09-09 14:58:17,057 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherRandom
2025-09-09 14:58:17,061 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 14:58:17,233 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherRandom
2025-09-09 14:58:17,469 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherIncludesTime
2025-09-09 14:58:23,351 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 14:58:44,259 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 14:58:44,267 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:58:44,267 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:58:44,268 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:58:44,281 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 14:58:44,289 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 14:58:44,430 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 14:58:44,672 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 14:58:44,814 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 14:58:44,955 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 14:58:44,999 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 14:59:21,153 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherRandom
2025-09-09 14:59:21,153 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherIncludesTime
2025-09-09 14:59:21,164 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 14:59:21,172 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CodeManagement
2025-09-09 14:59:21,330 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherRandom
2025-09-09 14:59:21,565 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/code_management_whetherIncludesTime
2025-09-09 14:59:31,047 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 14:59:58,176 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 15:00:00,105 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 15:00:35,067 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 15:00:39,249 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/detail/1952920532753633282
2025-09-09 15:00:58,109 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagement
2025-09-09 15:00:58,394 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/codeManagement/codeManagementList
2025-09-09 15:01:43,380 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 15:01:43,390 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 15:01:43,391 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 15:01:43,392 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 15:11:17,247 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-09 15:11:17,262 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-09 15:11:17,354 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-09 15:11:17,467 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-09 15:11:17,521 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-09 15:11:17,648 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-09 15:11:17,686 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 15:11:17,720 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-09 15:11:17,752 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-09 15:11:17,785 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-09 15:11:17,820 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 15:11:46,482 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-09 15:17:43,150 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-09 15:17:46,333 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-09 15:18:31,080 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-09 15:18:31,086 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-09 15:18:31,125 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-09 15:18:31,237 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-09 15:18:31,298 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-09 15:18:31,428 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-09 15:18:31,464 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 15:18:31,503 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-09 15:18:31,543 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-09 15:18:31,590 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-09 15:18:31,645 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-09 15:18:34,308 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-09 15:51:10,326 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 15:51:10,326 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 15:51:10,343 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 15:51:10,346 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 15:51:10,346 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 15:51:10,355 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 15:51:10,423 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 15:51:10,444 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 15:51:10,721 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 15:51:10,904 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 15:51:11,008 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 15:51:11,044 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 15:51:11,097 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 15:51:11,132 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 15:51:11,162 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 15:51:11,725 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:07:24,492 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:07:25,212 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:07:25,217 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:07:25,217 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:07:25,221 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:07:25,222 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:07:25,223 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-09 16:07:25,468 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:07:25,476 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:07:25,489 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:07:25,538 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:07:25,580 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:07:42,779 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 16:07:42,791 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 16:07:42,866 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 16:07:43,182 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 16:07:45,436 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 16:07:45,477 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:07:45,478 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:07:45,480 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:07:45,480 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 16:07:45,481 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 16:07:45,632 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:07:45,663 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:07:45,674 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:07:45,848 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:07:45,929 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 16:07:46,104 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 16:07:46,151 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:07:46,225 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 16:07:46,279 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:07:46,327 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:07:47,233 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:08:01,791 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:08:02,311 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:08:02,312 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:08:02,312 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:08:02,716 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:08:02,946 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:08:03,144 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:08:03,158 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:08:03,279 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:11,438 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:09:11,884 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:11,884 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:09:11,885 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:11,973 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:11,997 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:12,002 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:09:12,070 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:12,088 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:23,933 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:09:24,361 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:24,365 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:09:24,365 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:24,436 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:24,454 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:24,466 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:09:24,516 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:09:24,526 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:11:34,985 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 16:11:34,999 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 16:11:35,087 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 16:11:35,297 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 16:11:42,600 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:11:42,627 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:11:42,628 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:11:42,629 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:11:42,662 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:11:42,671 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:11:43,768 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:13:44,779 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 16:13:44,790 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 16:13:44,839 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 16:13:45,002 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 16:13:47,481 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 16:13:47,505 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:13:47,510 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:13:47,514 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 16:13:47,516 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 16:13:47,529 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:13:47,610 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:13:47,619 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:13:47,658 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:13:48,012 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:13:48,250 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 16:13:49,574 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 16:13:49,635 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:13:49,989 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 16:13:50,062 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:13:50,119 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:13:50,632 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:14:16,322 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:17:10,256 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:17:10,260 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:17:10,286 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 16:17:10,290 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 16:17:10,290 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 16:17:10,291 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:17:10,319 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:17:10,377 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:17:10,383 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 16:17:10,401 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 16:17:10,652 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 16:17:12,083 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 16:17:12,148 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:17:12,263 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 16:17:12,327 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 16:17:14,212 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-09 16:17:14,221 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 16:17:14,289 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-09 16:17:14,388 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-09 16:17:14,680 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:17:14,750 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 16:17:14,789 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 16:17:21,242 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 16:17:45,160 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-09 16:17:45,161 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-09 16:17:56,806 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/updateReviewResult
2025-09-09 16:18:03,023 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-09 16:19:09,932 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:19:09,940 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:19:09,942 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 16:19:09,949 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:19:09,956 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 16:19:09,967 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:19:10,025 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:19:10,087 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:19:10,244 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:19:10,295 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 16:19:10,376 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 16:19:10,412 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:19:10,476 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 16:19:10,508 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:19:10,543 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:19:10,871 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:19:13,695 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:19:29,623 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:19:45,722 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:20:09,726 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:20:16,016 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:20:25,129 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:20:28,289 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626566791170
2025-09-09 16:20:28,870 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:20:28,870 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:20:28,870 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:20:28,950 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626566791170
2025-09-09 16:20:40,672 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:20:41,831 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:20:41,915 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:20:42,070 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:26:58,931 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 16:26:58,933 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 16:26:58,937 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:26:58,948 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-09 16:26:58,969 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 16:26:58,971 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 16:26:59,091 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 16:26:59,307 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 16:26:59,452 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:26:59,500 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-09 16:26:59,552 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-09 16:27:37,082 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/entrustedUnit
2025-09-09 16:27:37,083 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_testPackage
2025-09-09 16:27:37,082 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/productModel
2025-09-09 16:27:37,083 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/standardSpecificationNumber
2025-09-09 16:27:37,082 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_processSchemeCategory
2025-09-09 16:27:37,101 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_status
2025-09-09 16:27:37,320 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardProcessPlan
2025-09-09 16:27:37,330 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 16:27:37,536 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:27:37,673 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/entrustedUnit
2025-09-09 16:27:37,742 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/productModel
2025-09-09 16:27:37,787 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/standardSpecificationNumber
2025-09-09 16:27:37,833 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_testPackage
2025-09-09 16:27:37,881 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_status
2025-09-09 16:27:37,929 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/department
2025-09-09 16:27:38,214 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/standardProcessPlanList
2025-09-09 16:27:43,144 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerProcessScheme/customerProcessSchemeList
2025-09-09 16:27:43,159 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CustomerProcessScheme
2025-09-09 16:27:43,240 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:27:51,067 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_processSchemeCategory
2025-09-09 16:27:51,069 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/entrustedUnit
2025-09-09 16:27:51,074 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/productModel
2025-09-09 16:27:51,076 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_testPackage
2025-09-09 16:27:51,076 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/standardSpecificationNumber
2025-09-09 16:27:51,076 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_status
2025-09-09 16:27:51,147 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 16:27:51,162 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardProcessPlan
2025-09-09 16:27:51,299 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:27:51,439 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/entrustedUnit
2025-09-09 16:27:51,584 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/productModel
2025-09-09 16:27:51,681 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/standardSpecificationNumber
2025-09-09 16:27:51,731 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_testPackage
2025-09-09 16:27:51,780 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_status
2025-09-09 16:27:51,829 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/department
2025-09-09 16:27:52,180 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/standardProcessPlanList
2025-09-09 16:28:08,425 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:28:08,432 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 16:28:08,432 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:28:08,433 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 16:28:08,438 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:28:08,438 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:28:08,497 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:28:08,509 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:28:08,609 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:28:08,682 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 16:28:08,868 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 16:28:08,921 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:28:09,030 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 16:28:09,080 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:28:09,114 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:28:09,458 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:28:55,507 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CustomerProcessScheme
2025-09-09 16:28:55,516 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerProcessScheme/customerProcessSchemeList
2025-09-09 16:28:55,616 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:28:56,877 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_processSchemeCategory
2025-09-09 16:28:56,879 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/entrustedUnit
2025-09-09 16:28:56,887 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/productModel
2025-09-09 16:28:56,889 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/standardSpecificationNumber
2025-09-09 16:28:56,889 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_testPackage
2025-09-09 16:28:56,905 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_status
2025-09-09 16:28:56,953 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-09 16:28:56,980 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardProcessPlan
2025-09-09 16:28:57,299 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:28:57,547 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/entrustedUnit
2025-09-09 16:28:57,595 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/productModel
2025-09-09 16:28:57,641 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/standardSpecificationNumber
2025-09-09 16:28:57,676 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_testPackage
2025-09-09 16:28:57,720 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_plan_status
2025-09-09 16:28:57,761 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/optionsList/department
2025-09-09 16:28:58,028 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessPlan/standardProcessPlanList
2025-09-09 16:34:27,354 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-09 16:34:27,377 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 16:34:27,377 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-09 16:34:27,502 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-09 16:34:27,582 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-09 16:34:29,292 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:34:29,299 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:34:29,306 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:34:29,308 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:34:29,328 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:34:29,334 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:34:29,432 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:35:34,315 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:35:43,872 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:35:45,139 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:35:49,880 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 16:35:49,890 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 16:35:49,945 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 16:35:50,123 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 16:35:55,947 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 16:35:55,997 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 16:35:55,996 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 16:35:56,018 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 16:35:56,019 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 16:35:56,018 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 16:35:56,205 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 16:35:56,206 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 16:35:56,218 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 16:35:57,254 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:35:57,443 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 16:35:58,168 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 16:35:58,448 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 16:35:58,561 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 16:35:58,607 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 16:35:58,638 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:35:58,939 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:36:01,691 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:36:02,190 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:02,190 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:36:02,190 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:02,271 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:36:10,311 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:10,321 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:10,405 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:10,428 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:50,279 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626566791170
2025-09-09 16:36:50,747 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:50,749 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:50,750 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:36:50,827 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:50,829 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626566791170
2025-09-09 16:36:50,865 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:53,437 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:36:53,615 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:37:03,076 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964862735118483457
2025-09-09 16:37:03,464 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:37:03,467 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:37:03,467 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:37:03,534 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:37:03,540 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:37:03,549 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964862735118483457
2025-09-09 16:37:03,603 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:37:03,617 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:11,832 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:39:12,637 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:12,793 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 16:39:12,937 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:39:12,962 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:13,095 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:12,982 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-09 16:39:12,991 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 16:39:13,859 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:14,480 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:14,577 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:14,675 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:39:30,869 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626516459521
2025-09-09 16:39:31,233 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:31,240 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:31,240 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:39:31,307 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:31,332 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626516459521
2025-09-09 16:39:31,332 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:31,367 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:39:32,881 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:42:41,414 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:42:44,069 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:42:58,458 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:43:39,407 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:43:48,677 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:52:25,796 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 16:52:42,366 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964884626566791170
2025-09-09 16:52:42,984 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:52:42,986 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:52:42,989 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 16:52:43,081 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964884626566791170
2025-09-09 16:52:43,485 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:52:43,485 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:52:43,595 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:52:43,600 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 16:53:00,042 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/submitTask
2025-09-09 16:56:26,513 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:06:07,339 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:06:09,305 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964213668872962050
2025-09-09 17:06:09,863 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:06:09,863 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:06:09,865 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 17:06:10,009 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964213668872962050
2025-09-09 17:06:10,013 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:06:10,014 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:06:10,090 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:06:10,150 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:06:13,733 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/add
2025-09-09 17:08:43,864 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:17:58,592 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:18:06,592 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:18:16,994 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:18:37,988 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:19:27,794 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 17:19:27,802 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 17:19:27,856 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 17:19:28,043 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 17:19:30,789 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 17:19:30,815 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 17:19:30,819 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 17:19:30,833 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 17:19:30,834 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 17:19:30,834 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 17:19:30,863 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 17:19:30,911 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 17:19:30,927 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 17:19:31,255 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 17:19:31,546 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 17:19:31,659 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 17:19:31,778 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 17:19:31,928 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 17:19:31,972 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 17:19:32,018 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 17:19:32,667 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:20:37,570 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:21:02,306 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:21:43,264 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:21:54,827 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:22:09,844 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:22:10,605 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:22:16,460 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:22:16,551 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:33:10,943 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/optionsList/department
2025-09-09 17:33:10,947 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:33:10,965 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SystemFile
2025-09-09 17:33:11,046 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:33:11,148 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:33:11,184 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:33:12,840 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/optionsList/department
2025-09-09 17:33:14,704 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:33:15,541 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:33:16,592 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:35:22,875 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:35:23,576 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:35:24,094 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:35:25,546 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:36:00,054 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 17:36:00,055 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 17:36:00,058 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 17:36:00,058 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 17:36:00,058 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 17:36:00,079 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 17:36:00,131 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 17:36:00,153 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 17:36:00,288 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 17:36:00,380 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 17:36:00,545 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 17:36:00,602 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 17:36:00,738 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 17:36:00,787 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 17:36:00,855 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 17:36:02,113 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:36:11,555 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:36:21,883 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:36:22,016 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:37:33,884 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:37:33,965 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:38:02,339 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:38:02,462 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:38:21,006 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:38:21,126 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:38:24,221 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:38:24,300 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:38:40,582 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:40:00,920 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:40:02,985 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:40:04,986 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:40:08,280 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:40:37,772 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:40:40,061 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:42:57,726 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-09 17:42:57,813 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:52:56,964 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:52:56,969 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/optionsList/department
2025-09-09 17:52:56,970 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SystemFile
2025-09-09 17:52:57,011 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:52:57,138 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:52:57,166 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:52:58,448 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/optionsList/department
2025-09-09 17:53:11,405 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:53:12,433 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:53:12,877 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:54:03,377 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/systemFile/export
2025-09-09 17:54:12,461 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/systemFile/export
2025-09-09 17:56:30,541 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 17:56:30,552 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 17:56:49,887 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 17:56:49,908 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 17:56:49,933 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 17:56:50,475 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 17:56:56,894 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 17:56:56,918 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SystemFile
2025-09-09 17:56:56,919 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:56:56,922 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/optionsList/department
2025-09-09 17:56:57,184 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:56:57,332 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_file_fileType
2025-09-09 17:56:57,550 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/systemFileList
2025-09-09 17:56:57,621 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/optionsList/department
2025-09-09 17:56:59,507 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/systemFile/export
2025-09-09 17:57:00,773 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 17:57:00,817 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 17:58:19,820 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 17:58:19,824 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 17:58:19,826 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 17:58:19,832 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 17:58:19,936 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-09 17:58:29,916 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 17:58:29,918 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 17:58:29,922 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 17:58:29,924 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 17:58:29,924 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 17:58:29,928 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 17:58:30,009 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 17:58:30,015 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 17:58:30,203 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 17:58:30,328 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 17:58:30,554 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 17:58:30,613 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 17:58:30,827 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 17:58:30,876 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 17:58:30,945 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 17:58:31,453 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 17:58:36,215 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964865719009263617
2025-09-09 17:58:36,797 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:58:36,797 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 17:58:36,797 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 17:58:36,797 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:58:36,799 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-09 17:58:36,800 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 17:58:37,031 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:58:37,042 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:58:37,046 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964865719009263617
2025-09-09 17:58:37,131 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:58:37,149 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 17:58:51,195 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/file/uploadFile
2025-09-09 17:59:16,135 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/add
2025-09-09 17:59:48,631 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/add
2025-09-09 18:04:17,505 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 18:04:19,905 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/export
2025-09-09 19:07:10,028 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:07:12,145 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964865719009263617
2025-09-09 19:07:13,631 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:07:13,634 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:07:13,634 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-09 19:07:13,635 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 19:07:13,657 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:07:13,661 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 19:07:14,056 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:07:14,056 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:07:14,111 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964865719009263617
2025-09-09 19:07:14,150 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:07:14,205 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:07:26,305 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/add
2025-09-09 19:07:26,668 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:07:51,179 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:07:55,086 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:08:00,254 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964869398248538113
2025-09-09 19:08:01,780 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:01,782 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:01,788 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964869398248538113
2025-09-09 19:08:01,799 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:08:01,804 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:08:01,805 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:08:01,906 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:08:01,910 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:08:01,964 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:08:01,978 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964869398248538113
2025-09-09 19:08:01,979 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:01,982 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:02,120 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:02,130 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:12,191 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964969263368417282
2025-09-09 19:08:13,376 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964969263368417282
2025-09-09 19:08:13,470 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:08:13,494 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:13,495 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:13,509 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:08:13,521 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:08:13,521 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:08:13,838 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:08:13,910 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964969263368417282
2025-09-09 19:08:13,912 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:13,912 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:08:13,955 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:13,988 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:14,030 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:16,694 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964969263305502722
2025-09-09 19:08:17,173 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:17,174 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:17,177 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:08:17,184 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:08:17,186 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964969263305502722
2025-09-09 19:08:17,202 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:08:17,252 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:08:17,253 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:08:17,256 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:08:17,270 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:17,272 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:17,328 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:17,337 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964969263305502722
2025-09-09 19:08:17,356 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:43,554 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/resumeTask/1964969263305502722
2025-09-09 19:08:44,160 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:08:48,674 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964969263305502722
2025-09-09 19:08:49,040 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:49,041 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:08:49,042 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:49,106 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:49,128 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:49,146 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964969263305502722
2025-09-09 19:08:49,191 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:08:49,200 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:09:04,890 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964862735168815105
2025-09-09 19:09:05,284 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964862735168815105
2025-09-09 19:09:05,289 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:09:05,289 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:09:05,289 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:09:05,289 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:09:05,289 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:09:05,382 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:09:05,391 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:09:05,413 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:09:05,418 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964862735168815105
2025-09-09 19:09:05,442 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:09:05,473 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:09:05,501 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:09:05,555 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:11:15,692 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 19:11:15,726 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 19:11:15,733 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 19:11:15,987 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 19:11:19,850 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 19:11:19,879 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 19:11:19,899 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:11:19,899 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 19:11:19,899 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:11:19,899 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:11:19,935 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:11:19,952 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:11:19,961 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 19:11:20,050 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 19:11:20,199 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 19:11:20,405 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 19:11:20,455 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 19:11:20,521 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 19:11:20,563 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 19:11:20,605 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 19:11:21,227 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:11:35,173 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:11:38,950 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:11:48,298 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 19:11:48,310 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 19:11:48,438 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 19:11:48,650 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 19:11:52,686 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 19:11:52,710 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:11:52,728 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:11:52,729 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 19:11:52,731 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:11:52,733 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:11:52,752 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:11:52,819 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 19:11:52,835 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 19:11:53,047 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 19:11:53,244 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 19:11:53,388 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 19:11:53,429 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 19:11:53,484 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 19:11:53,519 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 19:11:53,571 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 19:11:54,132 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:11:58,203 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:12:51,904 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-09 19:12:51,909 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-09 19:12:52,030 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-09 19:12:52,452 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-09 19:12:58,806 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-09 19:12:58,892 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:12:58,897 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:12:58,912 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:12:58,912 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:12:58,912 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-09 19:12:58,959 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-09 19:12:58,960 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:12:58,992 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-09 19:12:59,175 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-09 19:12:59,251 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-09 19:12:59,328 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-09 19:12:59,373 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-09 19:12:59,443 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-09 19:12:59,508 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-09 19:13:01,165 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-09 19:13:02,151 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:13:07,801 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964865719009263617
2025-09-09 19:13:08,300 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:08,311 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964865719009263617
2025-09-09 19:13:08,320 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:13:08,321 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:13:08,324 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:13:08,325 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:08,369 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:13:08,377 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:13:08,385 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:13:08,402 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:08,489 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964865719009263617
2025-09-09 19:13:08,489 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:08,571 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:08,578 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:11,235 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_operationType
2025-09-09 19:13:28,703 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/pauseTask
2025-09-09 19:13:29,652 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:13:31,923 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964865719009263617
2025-09-09 19:13:32,453 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964865719009263617
2025-09-09 19:13:32,469 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:13:32,469 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:13:32,469 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:32,471 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:13:32,474 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:32,588 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:13:32,596 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:13:32,611 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:13:32,623 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964865719009263617
2025-09-09 19:13:32,623 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:32,624 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:32,732 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:13:32,772 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:25:34,972 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/pauseTask
2025-09-09 19:25:49,297 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/pauseTask
2025-09-09 19:26:01,635 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-09 19:26:11,227 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964865719009263617
2025-09-09 19:26:12,214 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964865719009263617
2025-09-09 19:26:12,219 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:26:12,239 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:26:12,244 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-09 19:26:12,244 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-09 19:26:12,246 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-09 19:26:12,574 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-09 19:26:12,632 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-09 19:26:12,633 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964865719009263617
2025-09-09 19:26:12,635 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-09 19:26:12,744 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:26:12,768 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:26:12,810 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-09 19:26:12,841 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
