2025-09-08 10:06:42,012 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-09-08 10:06:42,033 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-09-08 10:07:16,435 WARN gateway [main] o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext [AbstractApplicationContext.java : 559] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
2025-09-08 10:09:33,588 WARN gateway [Thread-2] c.a.n.c.http.HttpClientBeanHolder [HttpClientBeanHolder.java : 108] [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-08 10:09:33,588 WARN gateway [Thread-13] c.a.nacos.common.notify.NotifyCenter [NotifyCenter.java : 145] [NotifyCenter] Start destroying Publisher
2025-09-08 10:09:33,589 WARN gateway [Thread-13] c.a.nacos.common.notify.NotifyCenter [NotifyCenter.java : 162] [NotifyCenter] Destruction of the end
2025-09-08 10:09:33,590 WARN gateway [Thread-2] c.a.n.c.http.HttpClientBeanHolder [HttpClientBeanHolder.java : 114] [HttpClientBeanHolder] Destruction of the end
2025-09-08 10:12:07,556 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-09-08 10:12:07,567 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-09-08 16:48:01,599 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-09-08 16:48:01,616 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
