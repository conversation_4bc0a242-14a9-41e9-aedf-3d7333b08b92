<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.system.mapper.SysUserMapper">
	<sql id="Base_Column_List">
		u.id as id,
		u.user_name as userName,
		u.chinese_name as chineseName,
		u.user_code as userCode,
		u.user_email as userEmail,
		u.user_phone as userPhone,
		u.user_mobile as userMobile,
		u.group_id as groupId,
		u.create_time as createTime,
		u.update_time as updateTime,
		u.gender as gender,
		u.comment as comment,
		u.avatar_url as avatarUrl
		,u.status
		,u.name
		,u.electronic_signature
		<!-- CODEX-TORCH-MAPPER-XML-REPLACE-POSITION-EXTENDED-FIELDS -->
	</sql>

	<select id="selectUserPage" parameterType="com.huatek.frame.modules.system.service.dto.SysUserDTO"
			resultType="com.huatek.frame.modules.system.domain.vo.SysUserVO">
		select
		<include refid="Base_Column_List" />
		,g.group_name as groupName
		from sys_user u
		left join sys_group g on u.group_id = g.id
		where u.deleted=#{deleted}
		<if test="userMobile != null and userMobile != '' ">
			and u.user_mobile like concat('%',#{userMobile},'%')
		</if>
		<if test="userEmail != null and userEmail != '' ">
			and u.user_email like concat('%',#{userEmail},'%')
		</if>
		<if test="userCode != null and userCode != '' ">
			and u.user_code like concat('%',#{userCode},'%')
		</if>
		<if test="userName != null and userName != '' ">
			and u.user_name like concat('%',#{userName},'%')
		</if>
		<if test="chineseName != null and chineseName != '' ">
			and u.chinese_name like concat('%',#{chineseName},'%')
		</if>
		<if test="userPhone != null and userPhone != '' ">
			and u.user_phone like concat('%',#{userPhone},'%')
		</if>
		<if test="gender != null and gender != '' ">
			and u.gender = #{gender}
		</if>
		<if test="groupId != null and groupId != '' ">
			and u.group_id = #{groupId}
		</if>
		<if test="comment != null and comment != ''">
			and u.comment  = #{comment}
		</if>
		<if test="status != null and status != '' ">
			 and u.status like concat('%', #{status}, '%')
		</if>

		<if test="name != null and name != '' ">
			 and u.name like concat('%', #{name}, '%')
		</if>

		<if test="electronicSignature != null and electronicSignature != '' ">
			 and u.electronic_signature like concat('%', #{electronicSignature}, '%')
		</if>

			<!-- CODEX-GENERATE-MAPPER-XML-REPLACE-POSITION-EXTENDED-QUERY-CONDITION -->
		${params.dataScope}
		order by
		u.create_time desc
	</select>

	<select id="selectUsersByRole" parameterType="com.huatek.frame.modules.system.service.dto.RoleDTO"
			resultType="com.huatek.frame.modules.system.domain.SysUser">
		select
		<include refid="Base_Column_List" />
		from sys_user u , sys_user_role sur, sys_role sr
		where u.deleted=#{deleted}
		and sur.role_id = sr.id and sur.user_id = u.id
		<if test="role != null and role != '' ">
			and sr.role like concat('%',#{role},'%')
		</if>
		<if test="id != null and id != '' ">
			and u.id like concat('%',#{id},'%')
		</if>
	</select>

	<select id="selectUserList" parameterType="com.huatek.frame.modules.system.service.dto.SysUserDTO"
			resultType="com.huatek.frame.modules.system.domain.vo.SysUserVO">
		select
		<include refid="Base_Column_List" />
		,g.group_name as groupId
		from sys_user u
		left join sys_group g on u.group_id = g.id
		<where>
			and 1=1
			<if test="userName != null and userName != ''">
				and u.user_name  like concat('%', #{userName} ,'%')
			</if>
			<if test="userCode != null and userCode != ''">
				and u.user_code  like concat('%', #{userCode} ,'%')
			</if>
			<if test="chineseName != null and chineseName != '' ">
				and u.chinese_name like concat('%',#{chineseName},'%')
			</if>
			<if test="userMobile != null and userMobile != ''">
				and u.user_mobile  like concat('%', #{userMobile} ,'%')
			</if>
			<if test="userPhone != null and userPhone != ''">
				and u.user_phone  like concat('%', #{userPhone} ,'%')
			</if>
			<if test="userEmail != null and userEmail != ''">
				and u.user_email  like concat('%', #{userEmail} ,'%')
			</if>
			<if test="gender != null and gender != ''">
				and u.gender  = #{gender}
			</if>
			<if test="groupId != null and groupId != ''">
				and u.group_id  = #{groupId}
			</if>
			<if test="comment != null and comment != ''">
				and u.comment  = #{comment}
			</if>
		<if test="status != null and status != '' ">
			 and u.status like concat('%', #{status}, '%')
		</if>

		<if test="name != null and name != '' ">
			 and u.name like concat('%', #{name}, '%')
		</if>

		<if test="electronicSignature != null and electronicSignature != '' ">
			 and u.electronic_signature like concat('%', #{electronicSignature}, '%')
		</if>

			<!-- CODEX-GENERATE-MAPPER-XML-REPLACE-POSITION-EXTENDED-QUERY-CONDITION -->
			${params.dataScope}
		</where>
	</select>
    <select id="findAllUsers" resultType="com.huatek.frame.modules.system.domain.vo.SysUserVO">
		select t.user_name as userName , t.id as id
		from sys_user t
	</select>
    <select id="getUsersByDepart" resultType="com.huatek.frame.modules.system.domain.vo.SysUserVO">
		select
		<include refid="Base_Column_List"/>
		from sys_user u
		where u.group_id = #{groupId}
	</select>
    <select id="findDepartById" resultType="com.huatek.frame.modules.system.domain.vo.SysGroupVO">
		select
		g.id as id,
		g.group_name as groupName
		from sys_user t
		left join sys_group g on t.group_id = g.id
		where t.id = #{userId}
	</select>
	<select id="selectUserIdsByRole" resultType="java.lang.String">
		select
	u.id
from
	sys_user u,
	sys_user_role ur,
	sys_role r
where
	u.id = ur.user_id
	and ur.role_id = r.id
	and r.role =#{roleCode}
	</select>

    <select id="selectUsersByRoleId" resultType="java.lang.String">
		select
		t.user_id
		from sys_user_role t
		where t.role_id = #{roleId}
	</select>

	<select id="selectCurrentUserRoles" resultType="java.lang.String">
		select sr.role from sys_user_role sur left join sys_role sr on sur.role_id  = sr.id where sur.user_id =#{currentUserId}
	</select>
</mapper>
