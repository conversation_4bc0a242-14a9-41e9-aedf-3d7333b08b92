package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 待制工单
* <AUTHOR>
* @date 2025-07-30
**/
@Setter
@Getter
@TableName("production_order")
public class ProductionOrder implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    
    /**
	 * 订单编号
     **/
    @TableField(value = "order_number"
    )
    private String orderNumber;

    /**
     * 产品列表id
     **/
    @TableField(value = "product"
    )
    private String product;

    /**
     * 试验方式
     **/
    @TableField(value = "test_methodology"
    )
    private String testsMethodology;
    /**
	 * 前置工单
     **/
    @TableField(value = "predecessor_work_order"
    )
    private String predecessorWorkOrder;

    
    /**
	 * 关联工单
     **/
    @TableField(value = "related_work_order"
    )
    private String relatedWorkOrder;

    
    /**
	 * PDA
     **/
    @TableField(value = "pda"
    )
    private BigDecimal pda;

    
    /**
	 * 是否加入排产
     **/
    @TableField(value = "whether_to_include_in_scheduling"
    )
    private String whetherToIncludeInScheduling;

    
    /**
	 * 预计完成时间
     **/
    @TableField(value = "estimated_completion_time"
    )
    private Date estimatedCompletionTime;

    
    /**
	 * 数量
     **/
    @TableField(value = "quantity"
    )
    private Integer quantity;

    
    /**
	 * 附件
     **/
    @TableField(value = "attachment"
    )
    private String attachment;

    
    /**
	 * 工单状态
     **/
    @TableField(value = "work_order_status"
    )
    private String workOrderStatus;


    /**
     * 报告状态
     **/
    @TableField(value = "report_status"
    )
    private String reportStatus;

    /**
	 * 负责人
     **/
    @TableField(value = "responsible_person"
    )
    private String responsiblePerson;
    /**
     * 制单人
     **/
    @TableField(value = "prepared_by"
    )
    private String preparedBy;

    
    /**
	 * 生产阶段
     **/
    @TableField(value = "production_stage"
    )
    private String productionStage;

    
    /**
	 * 是否同型同批二次检测
     **/
    @TableField(value = "wtstabr"
    )
    private String wtstabr;

    
    /**
	 * 是否入器件
     **/
    @TableField(value = "whether_to_enter_components"
    )
    private String whetherToEnterComponents;

    
    /**
	 * 是否入资料
     **/
    @TableField(value = "whether_to_enter_documents"
    )
    private String whetherToEnterDocuments;

    
    /**
	 * 实际完成时间
     **/
    @TableField(value = "completion_time",fill = FieldFill.INSERT
    )
    private Timestamp completionTime;

    
    /**
	 * 不合格率预警标记
     **/
    @TableField(value = "wmfcnr"
    )
    private String wmfcnr;

    
    /**
	 * 不可筛原因
     **/
    @TableField(value = "irretrievable_reason"
    )
    private String irretrievableReason;
    /**
     * 不可老化原因
     **/
    @TableField(value = "non_aging_reason"
    )
    private String nonAgingReason;

    /**
     * 封装形式
     **/
    @TableField(value = "package_form"
    )
    private String packageForm;

    /**
     * 来源工单
     **/
    @TableField(value = "source_order"
    )
    private String soruceOrder;

    /**
     * 申请人
     **/
    @TableField(value = "codex_torch_applicant"
    )
    private String codexTorchApplicant;

    /**
     * 待审批人
     **/
    @TableField(value = "codex_torch_approver"
    )
    private String codexTorchApprover;

    /**
     * 待审批人列表
     **/
    @TableField(value = "codex_torch_approvers"
    )
    private String codexTorchApprovers;

    /**
     * 流程状态
     **/
    @TableField(value = "codex_torch_approval_status"
    )
    private String codexTorchApprovalStatus;


    /**
     * 是否核算('1'为核算,'0'为未核算,默认为0)
     **/
    @TableField(value = "is_calculation"
    )
    private String isCalculation;

    /**
     * 核算时间
     **/
    @TableField(value = "calculation_time")
    private Timestamp calculationTime;


    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}