package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetImportVO;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.service.CapabilityAssetService;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-04
**/
@Api(tags = "能力资产管理")
@RestController
@RequestMapping("/api/capabilityAsset")
public class CapabilityAssetController {

	@Autowired
    private CapabilityAssetService capabilityAssetService;

	/**
	 * 能力资产列表
	 * 
	 * @param dto 能力资产DTO 实体对象
	 * @return
	 */
    @Log("能力资产列表")
    @ApiOperation(value = "能力资产列表查询")
    @PostMapping(value = "/capabilityAssetList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("capabilityAsset:list")
    public TorchResponse<List<CapabilityAssetVO>> query(@RequestBody CapabilityAssetDTO dto){
        return capabilityAssetService.findCapabilityAssetPage(dto);
    }

	/**
	 * 新增/修改能力资产
	 * 
	 * @param capabilityAssetDto 能力资产DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改能力资产")
    @ApiOperation(value = "能力资产新增/修改操作")
    @PostMapping(value = "/saveOrUpdate", produces = { "application/json;charset=utf-8" })
    @TorchPerm("capabilityAsset:add#capabilityAsset:edit")
    public TorchResponse saveOrUpdate(@RequestBody CapabilityAssetDTO capabilityAssetDto) throws Exception {
		// BeanValidatorFactory.validate(capabilityAssetDto);
		return capabilityAssetService.saveOrUpdate(capabilityAssetDto);
	}

	/**
	 * 查询能力资产详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("能力资产详情")
    @ApiOperation(value = "能力资产详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("capabilityAsset:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return capabilityAssetService.findCapabilityAsset(id);
	}

	/**
	 * 删除能力资产
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除能力资产")
    @ApiOperation(value = "能力资产删除操作")
    @TorchPerm("capabilityAsset:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return capabilityAssetService.delete(ids);
	}

    @ApiOperation(value = "能力资产联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return capabilityAssetService.getOptionsList(id);
	}




    @ApiOperation(value = "能力资产 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return capabilityAssetService.getLinkageData(linkageDataTableName, conditionalValue);
	}

    @Log("能力资产导出")
    @ApiOperation(value = "能力资产导出")
    @TorchPerm("capabilityAsset:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody CapabilityAssetDTO dto)
    {
        List<CapabilityAssetVO> list = capabilityAssetService.selectCapabilityAssetList(dto);
        ExcelUtil<CapabilityAssetVO> util = new ExcelUtil<CapabilityAssetVO>(CapabilityAssetVO.class);
        util.exportExcel(response, list, "能力资产数据");
    }

    @Log("能力资产导入")
    @ApiOperation(value = "能力资产导入")
    @TorchPerm("capabilityAsset:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<CapabilityAssetImportVO> util = new ExcelUtil<CapabilityAssetImportVO>(CapabilityAssetImportVO.class);
        List<CapabilityAssetImportVO> list = util.importExcel(file.getInputStream());
        return capabilityAssetService.importCapabilityAsset(list, unionColumns, false, "");
    }

    @Log("能力资产导入模板")
    @ApiOperation(value = "能力资产导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<CapabilityAssetImportVO> util = new ExcelUtil<CapabilityAssetImportVO>(CapabilityAssetImportVO.class);
        util.importTemplateExcel(response, "能力资产数据");
    }

    @Log("根据Ids获取能力资产列表")
    @ApiOperation(value = "能力资产 根据Ids批量查询")
    @PostMapping(value = "/capabilityAssetList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getCapabilityAssetListByIds(@RequestBody List<String> ids) {
        return capabilityAssetService.selectCapabilityAssetListByIds(ids);
    }
    
    @Log("根据多个产品型号和能力类型组合进行能力核验")
    @ApiOperation(value = "能力资产 根据多个产品型号和能力类型组合进行能力核验")
    @PostMapping(value = "/getCapabilityAssetListByMultiParams", produces = {"application/json;charset=utf-8"})
    public TorchResponse getCapabilityAssetListByMultiParams(@RequestBody List<CapabilityVerificationCheckDTO> queryParams) {
        return capabilityAssetService.selectCapabilityAssetListByMultiParams(queryParams);
    }

}