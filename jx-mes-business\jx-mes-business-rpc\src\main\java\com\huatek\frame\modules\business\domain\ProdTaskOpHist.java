package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 生产任务操作历史
* <AUTHOR>
* @date 2025-08-11
**/
@Setter
@Getter
@TableName("prod_task_op_hist")
public class ProdTaskOpHist implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 任务编号
     **/
    @TableField(value = "task_number"
    )
    private String taskNumber;

    
    /**
	 * 原因
     **/
    @TableField(value = "reason"
    )
    private String reason;

    
    /**
	 * 已完成数量
     **/
    @TableField(value = "completed_quantity"
    )
    private Integer completedQuantity;

    
    /**
	 * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    
    /**
	 * 操作类型
     **/
    @TableField(value = "operation_type"
    )
    private String operationType;

    /**
	 * 上一个状态
	 */
    @TableField(value = "previous_status"
    )
    private String previousStatus;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}