package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 外协申请VO实体类
* <AUTHOR>
* @date 2025-08-07
**/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("外协申请DTO实体类")
public class OutsourcingVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @Excel(name = "工序编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.IMPORT)
    private String processNumber;

    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String testType;

    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String orderNumber;

    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String entrustedUnit;

    /**
     * 试验依据
     **/
    @ApiModelProperty("试验依据")
    @Excel(name = "试验依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String testBasis;

    /**
     * 外协类型
     */
    @ApiModelProperty("外协类型")
    @Excel(name = "外协类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String entireOrProcess;

    /**
     * 外协部门
     **/
    @ApiModelProperty("外协部门")
    @Excel(name = "外协部门",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String outsourcingDepartment;

    /**
     * 外协工序
     **/
    @ApiModelProperty("外协工序")
    @Excel(name = "外协工序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String outsourcingProcess;

    /**
     * 外协原因
     **/
    @ApiModelProperty("外协原因")
    @Excel(name = "外协原因",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String outsourcingReason;

    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String productName;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String productModel;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String productionBatch;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String manufacturer;


    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    @Excel(name = "数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String quantity;

    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String productCategory;



    /**
     * 外协厂家
     **/
    @ApiModelProperty("外协厂家")
    @Excel(name = "外协厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String outsourcingManufacturer;

    /**
     * 预计价格
     **/
    @ApiModelProperty("预计价格")
    @Excel(name = "预计价格",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String estimatedPrice;

    /**
     * 预计结束时间
     **/
    @ApiModelProperty("预计结束时间")
    @Excel(name = "预计结束时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedEndTime;

    /**
     * 实际价格
     **/
    @ApiModelProperty("实际价格")
    @Excel(name = "实际价格",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String actualPrice;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String status;

    /**
     * 申请人
     **/
    @ApiModelProperty("申请人")
    @Excel(name = "申请人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String codexTorchApplicant;

    /**
     * 申请时间
     **/
    @ApiModelProperty("申请时间")
    @Excel(name = "申请时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.EXPORT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applicationTime;

    /**
     * 外协编号
     **/
    @ApiModelProperty("外协编号")
    private String outsourcingNumber;

    /**
     * 工单ID
     **/
    @ApiModelProperty("工单ID")

    private String orderId;

    /**
     * 标准规范编号
     */
    @ApiModelProperty("标准规范编号")
    private String specificationNumber;


    /**
     * 工序id
     */
    @ApiModelProperty("外协工序id")
    private String processId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String comment;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;


    /**
     * 待审批人
     **/
    @ApiModelProperty("待审批人")
    private String codexTorchApprover;

    /**
     * 审批人列表
     **/
    @ApiModelProperty("审批人列表")
    private String codexTorchApprovers;

    /**
     * 流程状态
     **/
    @ApiModelProperty("流程状态")
    private String codexTorchApprovalStatus;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}