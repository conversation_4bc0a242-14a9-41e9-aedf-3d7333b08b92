package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.business.domain.vo.OutsourcingVO;
import com.huatek.frame.modules.business.service.OutsourcingService;
import com.huatek.frame.modules.business.service.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;

import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
* <AUTHOR>
* @date 2025-08-07
**/
@Api(tags = "外协申请管理")
@RestController
@RequestMapping("/api/outsourcing")
public class OutsourcingController {

	@Autowired
    private OutsourcingService outsourcingApplicationService;

	/**
	 * 分页查询外协申请
	 * 
	 * @param requestParam 分页查询外协申请DTO
	 * @return
	 */
    @Log("外协申请列表")
    @ApiOperation(value = "外协申请列表查询")
    @PostMapping(value = "/outsourcingList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("outsourcingApplication:list")
    public TorchResponse<List<OutsourcingVO>> query(@RequestBody OutsourcingPageDTO requestParam){
        return outsourcingApplicationService.findOutsourcingApplicationPage(requestParam);
    }

	/**
	 * 新增/修改外协申请
	 * 
	 * @param requestParam 新增/修改外协DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改外协申请")
    @ApiOperation(value = "外协申请新增/修改操作")
    @PostMapping(value = "/addOrUpdateOutsourcing", produces = { "application/json;charset=utf-8" })
    @TorchPerm("outsourcingApplication:add#outsourcingApplication:edit")
    public TorchResponse add(@RequestBody AddOrUpdateOutsourcingDTO requestParam) throws Exception {
		return outsourcingApplicationService.saveOrUpdate(requestParam);
	}

	/**
	 * 查询外协申请详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("外协申请详情")
    @ApiOperation(value = "外协申请详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("outsourcingApplication:detail")
	public TorchResponse<OutsourcingVO> detail(@PathVariable(value = "id") String id) {
		return outsourcingApplicationService.findOutsourcingDetails(id);
	}

	/**
	 * 删除外协申请
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("批量删除外协申请")
    @ApiOperation(value = "外协申请删除操作")
    @TorchPerm("outsourcingApplication:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return outsourcingApplicationService.delete(ids);
	}

    @ApiOperation(value = "外协申请联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return outsourcingApplicationService.getOptionsList(id);
	}

    /**
     * 外协申请审批
     *
     * @param formApprovalDTO 表单审批DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("外协申请审批")
    @ApiOperation(value = "外协申请审批")
    @PostMapping(value = "/approval", produces = { "application/json;charset=utf-8" })
    @TorchPerm("outsourcingApplication:approval#outsourcingApplicationApproval:approval")
    public TorchResponse approve(@RequestBody FormApprovalDTO formApprovalDTO, @RequestHeader(value = Constant.TOKEN) String token) throws Exception {
        // BeanValidatorFactory.validate(formApprovalDTO);
        return outsourcingApplicationService.approve(formApprovalDTO, token);
    }

    /**
     * 提交审批外协申请
     *
     * @param outsourcingApplicationDto 外协申请DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("提交审批外协申请")
    @ApiOperation(value = "外协申请提交审批")
    @PostMapping(value = "/apply", produces = { "application/json;charset=utf-8" })
    @TorchPerm("outsourcingApplication:add#outsourcingApplication:edit")
    public TorchResponse apply(@RequestBody AddOrUpdateOutsourcingDTO outsourcingApplicationDto, @RequestHeader(value = Constant.TOKEN) String token) throws Exception {
        // BeanValidatorFactory.validate(outsourcingApplicationDto);
        return outsourcingApplicationService.apply(outsourcingApplicationDto, token);
    }

    @Log("批量提交外协申请")
    @ApiOperation("批量提交外协申请")
    @PostMapping(value = "/batchApply", produces = { "application/json;charset=utf-8" })
    public TorchResponse batchApply(@RequestBody List<String> ids, @RequestHeader(value = Constant.TOKEN) String token) throws  Exception{
        return outsourcingApplicationService.batchApply(ids, token);
    }

    @Log("外协申请导出")
    @ApiOperation(value = "外协申请导出")
    @TorchPerm("outsourcingApplication:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody OutsourcingDTO dto)
    {
        List<OutsourcingVO> list = outsourcingApplicationService.selectOutsourcingApplicationList(dto);
        ExcelUtil<OutsourcingVO> util = new ExcelUtil<OutsourcingVO>(OutsourcingVO.class);
        util.exportExcel(response, list, "外协申请数据");
    }

    @Log("外协申请导入")
    @ApiOperation(value = "外协申请导入")
    @TorchPerm("outsourcingApplication:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<OutsourcingVO> util = new ExcelUtil<OutsourcingVO>(OutsourcingVO.class);
        List<OutsourcingVO> list = util.importExcel(file.getInputStream());
        return outsourcingApplicationService.importOutsourcingApplication(list, unionColumns, true, "");
    }

    @Log("外协申请导入模板")
    @ApiOperation(value = "外协申请导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<OutsourcingVO> util = new ExcelUtil<OutsourcingVO>(OutsourcingVO.class);
        util.importTemplateExcel(response, "外协申请数据");
    }

    @Log("根据Ids获取外协申请列表")
    @ApiOperation(value = "外协申请 根据Ids批量查询")
    @PostMapping(value = "/outsourcingList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getOutsourcingApplicationListByIds(@RequestBody List<String> ids) {
        return outsourcingApplicationService.selectOutsourcingApplicationListByIds(ids);
    }

    @Log("批量修改外协工单")
    @ApiOperation(value = "批量修改外协工单:审批通过、审批驳回")
    @PostMapping(value = "/batchUpdateOutSourcing", produces = {"application/json;charset=utf-8"})
    public TorchResponse batchUpdateOutSourcing(@RequestBody OutsourcingUpdateDTO requestParam, @RequestHeader(value = Constant.TOKEN) String token) {
        return outsourcingApplicationService.batchUpdateOutSourcing(requestParam, token);
    }

    @Log("分页查询待审批状态下的外协工单")
    @ApiOperation(value = "查询待审批状态下的外协工单")
    @PostMapping(value = "/findPendingOutsourcings", produces = {"application/json;charset=utf-8"})
    public TorchResponse findPendingOutsourcings(@RequestBody OutsourcingPageDTO requestParam) {
        return outsourcingApplicationService.findPendingOutsourcings(requestParam);
    }

    @Log("分页查询审批历史")
    @ApiOperation(value = "分页查询审批历史")
    @PostMapping(value = "/findOutsourcingHistory", produces = {"application/json;charset=utf-8"})
    public TorchResponse findOutsourcingHistorys(@RequestBody OutsourcingPageDTO requestParam) {
        return outsourcingApplicationService.findOutsourcingHistorys(requestParam);
    }

//    @Log("新增外协申请并提交")
//    @ApiOperation(value = "新增外协申请并提交")
//    @PostMapping(value = "/addAndSubmitOutsourcing", produces = {"application/json;charset=utf-8"})
//    public TorchResponse addAndSubmitOutsourcing(@RequestBody AddOrUpdateOutsourcingDTO requestParam) {
//        return outsourcingApplicationService.addAndSubmitOutsourcing(requestParam);
//    }




}