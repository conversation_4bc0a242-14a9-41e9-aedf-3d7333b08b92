package com.huatek.frame.modules.system.service.impl;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.config.MinioProperties;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.oss.client.TosClientFactory;
import com.huatek.frame.common.oss.upload.IOssUpload;
import com.huatek.frame.common.oss.upload.OssUpload;
import com.huatek.frame.common.utils.*;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.constant.UserInfoConstants;
import com.huatek.frame.modules.system.domain.*;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.dto.RoleDTO;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.mapper.SysGroupMapper;
import com.huatek.frame.modules.system.mapper.RuleRoleMapper;
import com.huatek.frame.modules.system.mapper.SysUserMapper;
import com.huatek.frame.modules.system.mapper.UserRoleMapper;
import com.huatek.frame.modules.system.service.SysUserService;
import com.huatek.frame.modules.system.service.dto.DataRuleDTO;
import com.huatek.frame.modules.system.service.dto.SysUserDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Validator;

/**
 * 系统_用户 ServiceImpl
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:46
 */
@DubboService
//@CacheConfig(cacheNames = "user")
@RefreshScope
@Slf4j
public class SysUserServiceImpl implements SysUserService {

	private static final String GROUPPARENTID = "0";

	private static final String USERNAME = "admin";

	private static final String EMAIL_REGEX = "^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$";
	private static final String OSS_PATH = "user-profile-avatar/";

	@Autowired
	private SysUserMapper sysUserMapper;
	
	@Autowired
	private UserRoleMapper userRoleMapper;
	
	@Autowired
	private SysGroupMapper sysGroupMapper;
	
	@Autowired
	private RuleRoleMapper ruleRoleMapper;
	@Autowired
	private SecurityUser securityUser;
	@Autowired
	private MinioProperties minioProperties;

	@Autowired
	protected Validator validator;

	@Autowired
	private SysGroupService sysGroupService;

	@Override
//	@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "g", userAlias = "u")
	public TorchResponse<List<SysUser>> findUserPage(SysUserDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		dto.setDeleted(Constant.DEFAULT_NO);
		Page<SysUser> sysUsers = sysUserMapper.selectUserPage(dto);
		TorchResponse<List<SysUser>> response = new TorchResponse<List<SysUser>>();
		response.getData().setData(sysUsers);
		response.setStatus(200);
		response.getData().setCount(sysUsers.getTotal());
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
//	@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(SysUser entity) {
		String id = entity.getId();
		List<SysUser> lists = null;
		if (HuatekTools.isEmpty(id)) {
			lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_code", entity.getUserCode()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户编码重复");
			}
			lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_name", entity.getUserName()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户名称重复");
			}
			lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_mobile", entity.getUserMobile()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户手机号重复");
			}
			lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_email", entity.getUserEmail()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户邮箱重复");
			}
			String salt = HuatekTools.getUuid();
			entity.setPasswordSalt(salt);
			entity.setDeleted(Constant.DEFAULT_NO);
			String passWord = Md5.getMd5Code(salt + Constant.DEFAULT_PASSWORD);
			entity.setPassword(passWord);
			sysUserMapper.insert(entity);
		} else {
			lists = sysUserMapper.selectList(
					new QueryWrapper<SysUser>().eq("user_code", entity.getUserCode()).ne("id", entity.getId()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户编码重复");
			}
			lists = sysUserMapper.selectList(
					new QueryWrapper<SysUser>().eq("user_name", entity.getUserName()).ne("id", entity.getId()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户名称重复");
			}
			lists = sysUserMapper.selectList(
					new QueryWrapper<SysUser>().eq("user_mobile", entity.getUserMobile()).ne("id", entity.getId()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户手机号重复");
			}
			lists = sysUserMapper.selectList(
					new QueryWrapper<SysUser>().eq("user_email", entity.getUserEmail()).ne("id", entity.getId()).eq("deleted", Constant.DEFAULT_NO));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("用户邮箱重复");
			}
			String groupId = entity.getGroupId();
			SysUser sysUser = sysUserMapper.selectById(id);
			String gId = sysUser.getGroupId();
			if (!gId.equals(groupId)) {
				userRoleMapper.delete(new QueryWrapper<UserRole>().eq("user_id", id));
			}
			sysUserMapper.updateById(entity);
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
//	@Cacheable(key = "#p0")
	public TorchResponse<SysUserVO> findUser(String id) {
		SysUserVO vo = new SysUserVO();
		if (!HuatekTools.isEmpty(id)) {
			SysUser entity = sysUserMapper.selectById(id);
			BeanUtils.copyProperties(entity, vo);
		}

		Map<String, Object> map = Maps.newConcurrentMap();
		map.put("deleted", Constant.DEFAULT_NO);
		map.put("groupParentId", GROUPPARENTID);
		List<SysGroupVO> datas = sysGroupMapper.getParentGroups(map);
		vo.setDatas(datas);
		TorchResponse<SysUserVO>  response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
//	@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		for (String id : ids) {
			SysUser sysUser = sysUserMapper.selectById(id);
			String userName = sysUser.getUserName();
			if (org.apache.commons.lang3.StringUtils.equals(userName, USERNAME)) {
				throw new ServiceException("admin用户不能删除");
			}
			SysUser entity = new SysUser();
			entity.setDeleted(Constant.DEFAULT_YES);
			sysUserMapper.update(entity,new QueryWrapper<SysUser>().eq("id", id));
		}
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
//	@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse updateUserRole(String uid, List<DataRuleDTO> dtos) {
		Wrapper<UserRole> wrapper =new QueryWrapper<UserRole>().eq("user_id", uid);
		userRoleMapper.delete(wrapper);
		for (DataRuleDTO dto : dtos) {
			UserRole userRole = new UserRole();
			userRole.setUserId(uid);
			userRole.setRoleId(dto.getRoleId());
			ruleRoleMapper.delete(new QueryWrapper<RuleRole>().eq("role_id", dto.getRoleId()));
//			String[] rules = dto.getRules();
//			for (String rule : rules) {
//				RuleRole po = new RuleRole(dto.getRoleId(), rule);
//				ruleRoleMapper.insert(po);
//			}
			userRoleMapper.insert(userRole);
		}
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
//	@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public SysUser initOauthChannelUserInfo(SysUser sysUser) {
		List<SysUser> lists = null;
		lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_name", sysUser.getUserName()));
		if (!CollectionUtils.isEmpty(lists)) {
			SysUser oldSysUser = lists.stream().filter(u -> StringUtils.isNotBlank(u.getId())).findFirst().orElse(null);
			if (!ObjectUtils.isEmpty(oldSysUser)) {
				return oldSysUser;
			}
		}
		lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_code", sysUser.getUserCode()));
		if (!CollectionUtils.isEmpty(lists)) {
			SysUser oldSysUser = lists.stream().filter(u -> StringUtils.isNotBlank(u.getId())).findFirst().orElse(null);
			if (!ObjectUtils.isEmpty(oldSysUser)) {
				return oldSysUser;
			}
		}
		lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_mobile", sysUser.getUserMobile()));
		if (!CollectionUtils.isEmpty(lists)) {
			SysUser oldSysUser = lists.stream().filter(u -> StringUtils.isNotBlank(u.getId())).findFirst().orElse(null);
			if (!ObjectUtils.isEmpty(oldSysUser)) {
				return oldSysUser;
			}
		}
		lists = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_email", sysUser.getUserEmail()));
		if (!CollectionUtils.isEmpty(lists)) {
			SysUser oldSysUser = lists.stream().filter(u -> StringUtils.isNotBlank(u.getId())).findFirst().orElse(null);
			if (!ObjectUtils.isEmpty(oldSysUser)) {
				return oldSysUser;
			}
		}
		String salt = HuatekTools.getUuid();
		sysUser.setPasswordSalt(salt);
		sysUser.setDeleted(Constant.DEFAULT_NO);
		String passWord = Md5.getMd5Code(salt + Constant.DEFAULT_PASSWORD);
		sysUser.setPassword(passWord);
		//设置第三方授权登录用户的默认组织信息
		SysGroup sysGroup = sysGroupMapper.selectOne(new QueryWrapper<SysGroup>().eq("group_code", UserInfoConstants.OAUTH_CHANNEL_DEFAULT_GROUP));
		if (!ObjectUtils.isEmpty(sysGroup)) {
			sysUser.setGroupId(sysGroup.getId());
		}
		sysUserMapper.insert(sysUser);
		UserRole userRole = new UserRole();
		userRole.setUserId(sysUser.getId());
		userRole.setRoleId(UserInfoConstants.OAUTH_CHANNEL_DEFAULT_ROLE_ID);
		userRoleMapper.insert(userRole);
		// 设置用户角色
		return sysUser;
	}

	@Override
	public TorchResponse test() {
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse getProfile(String token) {
		JSONObject user = securityUser.currentUser(token);
		if (HuatekTools.isEmpty(user)){
			throw new ServiceException("当前用户超时，请重新登录");
		}
		String id = user.getString("id");
		SysUserVO vo = new SysUserVO();
		if (HuatekTools.isEmpty(id)) {
			throw new ServiceException("当前用户信息异常");

		}
		SysUser entity = sysUserMapper.selectById(id);
		BeanUtils.copyProperties(entity, vo);

		SysGroup sysGroup = sysGroupMapper.selectById(vo.getGroupId());
		vo.setGroupName(sysGroup.getGroupName());
		TorchResponse<SysUserVO>  response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@Override
	public TorchResponse updateProfile(SysUser sysUser) {
		String id = sysUser.getId();
		List lists = sysUserMapper.selectList(
				new QueryWrapper<SysUser>().eq("user_mobile", sysUser.getUserMobile()).ne("id", id).eq("deleted", Constant.DEFAULT_NO));
		if (!ObjectUtils.isEmpty(lists)) {
			throw new ServiceException("用户手机号重复");
		}
		lists = sysUserMapper.selectList(
				new QueryWrapper<SysUser>().eq("user_email", sysUser.getUserEmail()).ne("id", id).eq("deleted", Constant.DEFAULT_NO));
		if (!ObjectUtils.isEmpty(lists)) {
			throw new ServiceException("用户邮箱重复");
		}
		SysUser newSysUser = sysUserMapper.selectById(id);
		newSysUser.setUserMobile(sysUser.getUserMobile());
		newSysUser.setUserEmail(sysUser.getUserEmail());
		newSysUser.setGender(sysUser.getGender());
		sysUserMapper.updateById(newSysUser);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse avatar(MultipartFile file, String token) {
		try {
			JSONObject currentUser = securityUser.currentUser(token);
			if (HuatekTools.isEmpty(currentUser)){
				throw new ServiceException("用户登录超时，请重新登录");
			}
			String id = currentUser.getString("id");
			if (HuatekTools.isEmpty(id)){
				throw new ServiceException("当前登录用户信息异常");
			}
			SysUser sysUser = sysUserMapper.selectById(id);
			if (HuatekTools.isEmpty(sysUser)){
				throw new ServiceException("用户信息不存在");
			}

			MinioClient client = TosClientFactory.createTosClient(minioProperties.getAccessKeySecret(), minioProperties.getAccessKeyId(), minioProperties.getEndpoint());
			IOssUpload upload = new OssUpload();
			String photo = upload.uploadInputStream(client, minioProperties.getBucketName(),  OSS_PATH + UUID.randomUUID() + ".jpg",
					file.getInputStream(),"image/jpeg");
			sysUser.setAvatarUrl(photo);
			sysUserMapper.updateById(sysUser);
			TorchResponse response = new TorchResponse();
			response.setStatus(Constant.REQUEST_SUCCESS);
			JSONObject json = new JSONObject();
			json.put("imgUrl", photo);
			response.getData().setData(json);
			return response;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new ServiceException("上传用户头像失败");
		}
	}
	@Override
	public TorchResponse resetPwd(String userId) {
		if (HuatekTools.isEmpty(userId)) {
			throw new ServiceException("参数异常");
		}
		SysUser sysUser = sysUserMapper.selectById(userId);
		String passwordSalt = sysUser.getPasswordSalt();
		String passWord = Md5.getMd5Code(passwordSalt + Constant.DEFAULT_PASSWORD);
		sysUser.setPassword(passWord);
		sysUserMapper.updateById(sysUser);
		TorchResponse response = new TorchResponse<>();
		response.setStatus(HttpStatus.OK.value());
		response.setMessage("密码重置成功");
		return response;
	}

	@Override
	public List<SysUser> findUserList(RoleDTO roleDto) {
		return sysUserMapper.selectUsersByRole(roleDto);
	}

	@Override
	public List<SysUserVO> selectUserList(SysUserDTO dto) {
		return sysUserMapper.selectUserList(dto);
	}

	@Override
	public TorchResponse importUser(List<SysUserVO> list, List<String> unionColumns, boolean isUpdateSupport, String operName) {
		if (StringUtils.isNull(list) || list.size() == 0) {
			throw new ServiceException("导入系统用户数据不能为空！");
		}
		int successNum = 0;
		int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		for (SysUserVO vo : list) {
			try {
				SysUser sysUser = new SysUser();
				if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
					failureNum ++;
					continue;
				}
				BeanUtils.copyProperties(vo, sysUser);
				QueryWrapper<SysUser> wrapper = new QueryWrapper();
				SysUser oldSysUser = null;
				// 验证是否存在这条数据
				if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
					for (String unionColumn: unionColumns) {
						try {
							Field field = SysUserVO.class.getDeclaredField(unionColumn);
							field.setAccessible(true);
							Object value = field.get(vo);
							wrapper.eq(unionColumn, value);
						} catch (Exception e) {
							throw new ServiceException("导入数据失败");
						}
					}
					oldSysUser = sysUserMapper.selectOne(wrapper);
				}
				if (StringUtils.isNull(oldSysUser)) {
					BeanValidators.validateWithException(validator, vo);
					sysUserMapper.insert(sysUser);
					successNum++;
					successMsg.append("<br/>" + successNum + "、用户名 " + vo.getUserName() + " 导入成功");
				} else if (isUpdateSupport) {
					BeanValidators.validateWithException(validator, vo);
					BeanUtil.copyProperties(vo, oldSysUser, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
					sysUserMapper.updateById(oldSysUser);
					successNum++;
					successMsg.append("<br/>" + successNum + "、用户名 " + vo.getUserName() + " 更新成功");
				}  else {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、用户名 " + vo.getUserName() + " 已存在");
				}
			} catch (Exception e)  {
				failureNum++;
				String msg = "<br/>" + failureNum + "、用户名 " + vo.getUserName() + " 导入失败：";
				failureMsg.append(msg + e.getMessage());
				log.error(msg, e);
			}
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
		}

		Map<String, Object> map = new HashMap<>();
		map.put("success", "导入成功 " + successNum + "条数据");
		map.put("failure", failureMsg);
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(map);
		return response;
	}

    @Override
    public String getCurrentUserGroupId() {

		return SecurityContextHolder.getCurrentUserGroupId();
    }

    @Override
    public TorchResponse<List<SysUserVO>> findAllUsers() {
		List<SysUserVO> results = sysUserMapper.findAllUsers();

		TorchResponse<List<SysUserVO>> response = new TorchResponse<>();
		response.getData().setData(results);
		return response;

    }

	@Override
	public SysUser selectById(String id) {
		return sysUserMapper.selectById(id);
	}

    @Override
    public List<SysUserVO> getUsersByDepart(String requestParam) {
        return sysUserMapper.getUsersByDepart(requestParam);

    }

    @Override
    public List<SysUserVO> getUsersByNames(String[] names) {
		LambdaQueryWrapper<SysUser> queryWrapper = Wrappers.lambdaQuery(SysUser.class)
				.in(SysUser::getUserName, names);
		List<SysUser> users = sysUserMapper.selectList(queryWrapper);

		List<SysUserVO> response = users.stream().map(item -> BeanUtil.toBean(item, SysUserVO.class)).collect(Collectors.toList());
		return response;

	}

	@Override
	public TorchResponse<SysGroupVO> findDepartById(String userId) {

		SysGroupVO sysGroupVO = sysUserMapper.findDepartById(userId);
		TorchResponse<SysGroupVO> response = new TorchResponse<>();
		response.getData().setData(sysGroupVO);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}


	private boolean linkedDataValidityVerification(SysUserVO vo, int failureNum, StringBuilder failureMsg) {
		int failureRecord = 0;
		StringBuilder failureRecordMsg = new StringBuilder();
		StringBuilder failureNotNullMsg = new StringBuilder();
		if (HuatekTools.isEmpty(vo.getUserName())) {
			failureRecord++;
			failureNotNullMsg.append("<br/>" + failureRecord + "=>用户名不能为空!");
		}
		if (HuatekTools.isEmpty(vo.getUserCode())) {
			failureRecord++;
			failureNotNullMsg.append("<br/>" + failureRecord + "=>用户编码不能为空!");
		}
		if (HuatekTools.isEmpty(vo.getUserMobile())) {
			failureRecord++;
			failureNotNullMsg.append("<br/>" + failureRecord + "=>手机号不能为空!");
		}
		if (HuatekTools.isEmpty(vo.getUserEmail())) {
			failureRecord++;
			failureNotNullMsg.append("<br/>" + failureRecord + "=>邮箱不能为空!");
		}
		if (!HuatekTools.isEmpty(vo.getGroupId())) {
			TorchResponse<List<CascadeOptionsVO>> groupsCascade = sysGroupService.findGroupsCascade();
			List<CascadeOptionsVO> list = groupsCascade.getData().getData();
			String[] groupIds = vo.getGroupId().split(",");
			if (CollectionUtils.isEmpty(list)) {
				failureRecord++;
				failureRecordMsg.append("<br/>" + failureRecord + "=>所属组织=" + vo.getGroupId() + "; ");
			} else {
				Map<String, String> cascadeOptionsMap = new HashMap<>();
				cascadeOptionsMap = getAllCascadeOptions(list, cascadeOptionsMap);
				for (String groupId : groupIds) {
					if (cascadeOptionsMap.containsValue(groupId)) {
						for (Map.Entry entry : cascadeOptionsMap.entrySet()) {
							if (entry.getValue().equals(groupId)) {
								vo.setGroupId(entry.getKey().toString());
								break;
							}
						}
					} else {
						failureRecord++;
						failureRecordMsg.append("<br/>" + failureRecord + "=>所属组织=" + groupId + "; ");
					}
				}
			}
		}
		if (failureRecord > 0) {
			failureNum ++;
			failureMsg.append("<br/>" + failureNum + "、");
			if (failureNotNullMsg.length() > 0) {
				failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
			}
			if (failureRecordMsg.length() > 0) {
				failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
			}
			return true;
		}
		return false;
	}


	private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
		for (CascadeOptionsVO cascadeOptionsVO : list) {
			cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
			List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
			if (!CollectionUtils.isEmpty(children)) {
				getAllCascadeOptions(children, cascadeOptionsMap);
			}
		}
		return cascadeOptionsMap;
	}

	public static void main(String[] args) {
		String md5Code = Md5.getMd5Code("b8bb3538b2cc47bdbf1cd946a566e44c" + "Huatek0@");
		System.err.println(md5Code);
	}
	@Override
	public List<String> selectUserIdsByRole(String roleCode) {
		return sysUserMapper.selectUserIdsByRole(roleCode);
	}

    @Override
    public List<String> selectUsersByRoleId(String roleId) {
        return sysUserMapper.selectUsersByRoleId(roleId);

    }
	@Override
	public List<String> selectCurrentUserRoles(String currentUserId) {
		return sysUserMapper.selectCurrentUserRoles(currentUserId);

	}
}