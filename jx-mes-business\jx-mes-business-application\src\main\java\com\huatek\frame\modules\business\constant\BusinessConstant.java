package com.huatek.frame.modules.business.constant;

import java.io.File;

public class BusinessConstant {


    //能力资产编号
    public static final String CAPABILITY_VERIFICATION_NLZC = "NLZC";
    //能力开发任务编号
    public static final String CAPABILITY_VERIFICATION_NLKF = "NLKF";
    //生产任务编号
    public static final String CAPABILITY_SCRW = "SCRW";
    //生产部班组长 角色
    public static final String ROLE_SHENGCHANBUBANZUZHANG = "生产部班组长";
    //可靠性班组长 角色
    public static final String ROLE_KEKAOXINGBANZUZHANG = "可靠性班组长";
    //任务审批人 角色
    public static final String ROLE_RENWUSHENPIREN = "任务审批人";
    //能力开发
    public static final String ROLE_NLKFGLRY = "能力开发管理人员";
    public static final String ROLE_NLPS = "能力评审";
    //待办
    public static final String PRODUCTIONTASK_TODO = "0";
    //全部
    public static final String PRODUCTIONTASK_ALL = "1";

    public static final String ADMIN = "admin";
    public static final String PRODUCTION_TASK_MANAGE_MENU_NAME = "生产管理-生产任务管理";
    public static final String CAPABILITY_REVIEW_MENU_NAME = "能力评审";

    public static final String CAPABILITY_REVIEW_FILE = "文件评审";
    //PDA工序名称
    public static final String PDA_PROCESS_NAME = "PDA";
    public static final String PDA_PROCESS_CODE = "pda_process";
    //市场结算角色
    public static final String ROLE_SHICHANGJIESUAN = "市场结算";
    //调度角色
    public static final String ROLE_DIAODU = "调度";

    public static String RESOURCESPATH = System.getProperty("user.dir") + File.separator + "jx-mes-business" +
            File.separator + "jx-mes-business-application" + File.separator + "resources" + File.separator;

    public static String LOGOPATH = RESOURCESPATH + "image/logo.png";
    public static String TEMPLATEPATH = RESOURCESPATH + "template" + File.separator;

    public static final String DPA = "DPA";
    public static final String AUTO_REVIEW_TXXT = "系统自动评审通过";

    //默认评审人
    public static final String EVOREVIEWER = "f52b1f9329cb4cd68d2fc660d9c2aac5";
    /**
     * 手册：Q/JX-QM-nn
     * <p>
     * 程序文件：Q/JX-QPnn
     * <p>
     * 作业指导书：Q/JX-nnZY-nnn
     * <p>
     * 表单：Q/JX-nnJL
     */
    public static final Integer FILE_ZIMU_COUNT_SHOUCE = 10;
    public static final Integer FILE_ZIMU_COUNT_CHENGXU = 9;
    public static final Integer FILE_ZIMU_COUNT_ZHIYINSHU = 13;
    public static final Integer FILE_ZIMU_COUNT_BIAODAN = 9;
}
