package com.huatek.frame.modules.business.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.modules.business.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;


@Data
@ApiModel("新增/修改外协申请DTO实体类")
public class AddOrUpdateOutsourcingDTO extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 生产工单编号
     */
    @ApiModelProperty("生产工单编号")
    private String workOrderNumber;

    /**
     * 工单id
     **/
    @ApiModelProperty("工单id")
    private String orderId;

    private String[] orderIds;

    /**
     * 工序id
     */
    @ApiModelProperty("工序id")
    private String processId;

    /**
     * 外协工序
     **/
    @ApiModelProperty("外协工序")
    private String outsourcingProcess;

    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    private String quantity;

    /**
     * 外协原因
     **/
    @ApiModelProperty("外协原因")
    private String outsourcingReason;


    /**
     * 外协厂家
     **/
    @ApiModelProperty("外协厂家")
    private String outsourcingManufacturer;

    /**
     * 外协部门
     **/
    @ApiModelProperty("外协部门")
    private String outsourcingDepartment;

    /**
     * 预计价格
     **/
    @ApiModelProperty("预计价格")
    private String estimatedPrice;




    /**
     * 预计结束时间
     **/
    @ApiModelProperty("预计结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedEndTime;

    /**
     * 实际价格
     **/
    @ApiModelProperty("实际价格")
    private String actualPrice;

    /**
     * 整单外协/工序外协
     * 0：整单外协
     * 1：工序外协
     */
    @ApiModelProperty("整单外协/工序外协")
    private String entireOrProcess;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String comment;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

    /**
     * 申请人
     **/
    @ApiModelProperty("申请人")
    private String codexTorchApplicant;

    /**
     * 待审批人
     **/
    @ApiModelProperty("待审批人")
    private String codexTorchApprover;

    /**
     * 审批人列表
     **/
    @ApiModelProperty("审批人列表")
    private String codexTorchApprovers;

    /**
     * 流程状态
     **/
    @ApiModelProperty("流程状态")
    private String codexTorchApprovalStatus;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

}
